<template>
  <div class="waveform-chart">
    <div class="chart-header">
      <h3 class="chart-title">{{ title }}</h3>
      <div class="chart-value">
        <AnimatedNumber
          :value="currentValue"
          :color="color"
          class="current-value"
        />
        <span class="unit">{{ unit }}</span>
        <span class="max-value">/ {{ formattedMaxValue }}</span>
      </div>
    </div>

    <div class="chart-content">
      <div class="chart-container" ref="chartContainer">
        <div v-if="!data || data.length === 0" class="no-data">
          <div class="no-data-icon">📊</div>
          <div class="no-data-text">暂无数据</div>
        </div>
      </div>

      <div class="waveform-params">
        <div class="param-item">
          <span class="param-label">前沿</span>
          <span class="param-value">{{ formattedRiseTime }}</span>
          <span class="param-unit">ns</span>
        </div>
        <div class="param-item">
          <span class="param-label">半宽</span>
          <span class="param-value">{{ formattedHalfWidth }}</span>
          <span class="param-unit">ns</span>
        </div>
        <div class="param-item">
          <span class="param-label">幅值</span>
          <span class="param-value">{{ formattedAmplitude }}</span>
          <span class="param-unit">{{ unit }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import AnimatedNumber from './AnimatedNumber.vue'
import { formatVoltage, formatCurrent } from '../utils/formatters.js'

// Props
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  data: {
    type: Array,
    default: () => []
  },
  color: {
    type: String,
    default: '#00F0FF'
  },
  unit: {
    type: String,
    default: 'V'
  },
  maxValue: {
    type: Number,
    default: 1000
  }
})

// 响应式引用
const chartContainer = ref(null)
let chartInstance = null

// 计算属性
const currentValue = computed(() => {
  if (!props.data || props.data.length === 0) return 0
  return parseFloat(props.data[props.data.length - 1]?.value || 0)
})

const peakValue = computed(() => {
  if (!props.data || props.data.length === 0) return 0
  return Math.max(...props.data.map(item => parseFloat(item.value)))
})

const avgValue = computed(() => {
  if (!props.data || props.data.length === 0) return 0
  const sum = props.data.reduce((acc, item) => acc + parseFloat(item.value), 0)
  return sum / props.data.length
})

const formattedCurrentValue = computed(() => {
  return formatValue(currentValue.value)
})

const formattedMaxValue = computed(() => {
  return formatValue(props.maxValue)
})

const formattedPeakValue = computed(() => {
  return formatValue(peakValue.value)
})

const formattedAvgValue = computed(() => {
  return formatValue(avgValue.value)
})

// 波形参数计算
const riseTime = computed(() => {
  // 前沿时间计算 (10%-90%上升时间)
  // 这里先返回模拟值，后续可以实现真实算法
  if (!props.data || props.data.length === 0) return 0
  return 2.5 // 模拟值，单位ns
})

const halfWidth = computed(() => {
  // 半宽计算 (50%幅值处的脉冲宽度)
  // 这里先返回模拟值，后续可以实现真实算法
  if (!props.data || props.data.length === 0) return 0
  return 15.8 // 模拟值，单位ns
})

const amplitude = computed(() => {
  // 幅值就是峰值
  return peakValue.value
})

const formattedRiseTime = computed(() => {
  return riseTime.value.toFixed(1)
})

const formattedHalfWidth = computed(() => {
  return halfWidth.value.toFixed(1)
})

const formattedAmplitude = computed(() => {
  return formatValue(amplitude.value)
})

// 格式化数值
const formatValue = (value) => {
  if (props.unit === 'V') {
    return formatVoltage(value)
  } else if (props.unit === 'A') {
    return formatCurrent(value)
  }
  return `${value.toFixed(2)} ${props.unit}`
}

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return
  
  chartInstance = echarts.init(chartContainer.value)
  
  const option = {
    backgroundColor: 'transparent',
    grid: {
      left: '10%',
      right: '10%',
      top: '10%',
      bottom: '20%',
      borderColor: props.color + '30'
    },
    xAxis: {
      type: 'value',
      name: '时间(s)',
      nameTextStyle: { 
        color: props.color,
        fontSize: 10
      },
      axisLine: { 
        lineStyle: { color: props.color + '60' }
      },
      axisTick: { 
        lineStyle: { color: props.color + '60' }
      },
      axisLabel: { 
        color: props.color + 'CC',
        fontSize: 10
      },
      splitLine: {
        lineStyle: {
          color: props.color + '20'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: props.unit,
      nameTextStyle: { 
        color: props.color,
        fontSize: 10
      },
      axisLine: { 
        lineStyle: { color: props.color + '60' }
      },
      axisTick: { 
        lineStyle: { color: props.color + '60' }
      },
      axisLabel: { 
        color: props.color + 'CC',
        fontSize: 10,
        formatter: (value) => {
          if (props.unit === 'V') {
            if (value >= 1000000) return `${(value / 1000000).toFixed(1)}M`
            if (value >= 1000) return `${(value / 1000).toFixed(1)}K`
          } else if (props.unit === 'A') {
            if (value >= 1000) return `${(value / 1000).toFixed(1)}K`
          }
          return value.toFixed(0)
        }
      },
      splitLine: {
        lineStyle: {
          color: props.color + '20'
        }
      }
    },
    series: [{
      type: 'line',
      data: [],
      smooth: true,
      lineStyle: {
        color: props.color,
        width: 3,
        shadowColor: props.color,
        shadowBlur: 15,
        shadowOffsetY: 3
      },
      itemStyle: {
        color: props.color,
        borderColor: props.color,
        borderWidth: 3,
        shadowColor: props.color,
        shadowBlur: 8
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: props.color + '60'
          }, {
            offset: 0.5,
            color: props.color + '30'
          }, {
            offset: 1,
            color: props.color + '05'
          }]
        }
      },
      symbol: 'none',
      animation: true,
      animationDuration: 800,
      animationEasing: 'cubicOut',
      animationDelay: (idx) => idx * 5
    }],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(10, 25, 46, 0.9)',
      borderColor: props.color,
      textStyle: {
        color: '#fff'
      },
      formatter: (params) => {
        const point = params[0]
        return `
          <div style="padding: 5px;">
            <div style="color: ${props.color}; font-weight: bold;">${props.title}</div>
            <div>时间: ${point.data[0]}s</div>
            <div>数值: ${formatValue(point.data[1])}</div>
          </div>
        `
      }
    }
  }
  
  chartInstance.setOption(option)
  
  // 响应式调整
  const resizeObserver = new ResizeObserver(() => {
    chartInstance?.resize()
  })
  resizeObserver.observe(chartContainer.value)
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance || !props.data) return
  
  const chartData = props.data.map(item => [
    parseFloat(item.time),
    parseFloat(item.value)
  ])
  
  chartInstance.setOption({
    series: [{
      data: chartData
    }]
  })
}

// 监听数据变化
watch(() => props.data, () => {
  updateChart()
}, { deep: true })

watch(() => props.color, () => {
  if (chartInstance) {
    chartInstance.dispose()
    nextTick(() => {
      initChart()
      updateChart()
    })
  }
})

// 生命周期
onMounted(() => {
  nextTick(() => {
    initChart()
    updateChart()
  })
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
})
</script>

<style scoped>
.waveform-chart {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: transparent;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 240, 255, 0.3);
}

.chart-title {
  font-size: 14px;
  font-weight: bold;
  color: #00F0FF;
  margin: 0;
  text-shadow: 0 0 5px rgba(0, 240, 255, 0.5);
}

.chart-value {
  display: flex;
  align-items: baseline;
  gap: 5px;
}

.current-value {
  font-size: 16px;
  font-weight: bold;
  color: #39FF14;
  text-shadow: 0 0 5px rgba(57, 255, 20, 0.5);
}

.unit {
  font-size: 12px;
  color: #18FFFF;
  margin-left: 2px;
}

.max-value {
  font-size: 12px;
  color: #666;
}

.chart-content {
  display: flex;
  flex: 1;
  gap: 15px;
  min-height: 150px;
  height: 200px;
  align-items: stretch;
}

.chart-container {
  flex: 1;
  min-height: 150px;
  height: 200px;
  position: relative;
}

.waveform-params {
  width: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: rgba(0, 240, 255, 0.05);
  border: 1px solid rgba(0, 240, 255, 0.2);
  border-radius: 8px;
  backdrop-filter: blur(5px);
  height: 100%;
  box-sizing: border-box;
}

.param-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 12px 8px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 6px;
  border: 1px solid rgba(0, 240, 255, 0.1);
  min-height: 60px;
  width: 100%;
}

.param-label {
  font-size: 10px;
  color: #00F0FF;
  margin-bottom: 4px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.param-value {
  font-size: 14px;
  font-weight: bold;
  color: #39FF14;
  text-shadow: 0 0 5px rgba(57, 255, 20, 0.5);
  margin-bottom: 2px;
}

.param-unit {
  font-size: 9px;
  color: #18FFFF;
  opacity: 0.8;
}

.no-data {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #666;
}

.no-data-icon {
  font-size: 24px;
  margin-bottom: 10px;
}

.no-data-text {
  font-size: 12px;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .chart-content {
    flex-direction: column;
    gap: 10px;
  }

  .waveform-params {
    width: 100%;
    flex-direction: row;
    justify-content: space-around;
    padding: 10px;
  }

  .param-item {
    flex: 1;
    margin: 0 5px;
  }
}
</style>
