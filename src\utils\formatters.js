// 数据格式化工具函数

// 格式化电压值
export function formatVoltage(value) {
  const num = parseFloat(value)
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)} MV`
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(1)} KV`
  }
  return `${num.toFixed(1)} V`
}

// 格式化电流值
export function formatCurrent(value) {
  const num = parseFloat(value)
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)} KA`
  }
  return `${num.toFixed(1)} A`
}

// 格式化气压值
export function formatPressure(value) {
  const num = parseFloat(value)
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(2)} MPa`
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(1)} KPa`
  }
  return `${num.toFixed(0)} Pa`
}

// 格式化时间
export function formatTime(seconds) {
  const mins = Math.floor(seconds / 60)
  const secs = (seconds % 60).toFixed(1)
  return mins > 0 ? `${mins}:${secs.padStart(4, '0')}` : `${secs}s`
}

// 格式化百分比
export function formatPercentage(value, total) {
  return `${((value / total) * 100).toFixed(1)}%`
}

// 数值动画函数
export function animateNumber(start, end, duration, callback) {
  const startTime = Date.now()
  const difference = end - start
  
  function update() {
    const elapsed = Date.now() - startTime
    const progress = Math.min(elapsed / duration, 1)
    
    // 使用缓动函数
    const easeProgress = 1 - Math.pow(1 - progress, 3)
    const current = start + difference * easeProgress
    
    callback(current)
    
    if (progress < 1) {
      requestAnimationFrame(update)
    }
  }
  
  requestAnimationFrame(update)
}

// 生成随机ID
export function generateId() {
  return Math.random().toString(36).substr(2, 9)
}

// 防抖函数
export function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 节流函数
export function throttle(func, limit) {
  let inThrottle
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}
