<template>
  <div class="pulse-waveform">
    <div class="waveform-header">
      <h3 class="waveform-title">{{ pulseTitle }}</h3>
      <div class="pulse-indicator" :class="{ active: isPulseActive }">
        <span class="indicator-dot"></span>
        <span class="indicator-text">{{ pulseStatus }}</span>
      </div>
    </div>
    
    <div class="waveform-content">
      <!-- 脉冲数值显示 -->
      <div class="pulse-value">
        <div class="value-display">
          <span class="value-number">{{ formattedPulseValue }}</span>
          <span class="value-unit">V</span>
        </div>
        <div class="value-label">峰值电压</div>
      </div>
      
      <!-- 波形图表 -->
      <div class="pulse-chart" ref="chartContainer"></div>
    </div>
    
    <!-- 脉冲参数 -->
    <div class="pulse-parameters">
      <div class="param-item">
        <span class="param-label">脉宽:</span>
        <span class="param-value">{{ pulseWidth }}</span>
      </div>
      <div class="param-item">
        <span class="param-label">频率:</span>
        <span class="param-value">{{ pulseFrequency }}</span>
      </div>
      <div class="param-item">
        <span class="param-label">能量:</span>
        <span class="param-value">{{ pulseEnergy }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { EXPERIMENT_PHASES } from '../config/experimentConfig.js'
import { formatVoltage } from '../utils/formatters.js'

// Props
const props = defineProps({
  pulseData: {
    type: Array,
    default: () => []
  },
  phase: {
    type: String,
    required: true
  }
})

// 响应式数据
const chartContainer = ref(null)
let chartInstance = null

// 计算属性
const pulseTitle = computed(() => {
  switch (props.phase) {
    case EXPERIMENT_PHASES.INTERMEDIATE_CHARGING:
      return '一级电容开关脉冲'
    case EXPERIMENT_PHASES.FINAL_CHARGING:
      return '中储电容开关脉冲'
    default:
      return '开关脉冲波形'
  }
})

const currentPulseValue = computed(() => {
  if (!props.pulseData || props.pulseData.length === 0) return 0
  return Math.max(...props.pulseData.map(item => parseFloat(item.value)))
})

const formattedPulseValue = computed(() => {
  return formatVoltage(currentPulseValue.value)
})

const isPulseActive = computed(() => {
  return currentPulseValue.value > 1000 // 大于1KV时认为有脉冲
})

const pulseStatus = computed(() => {
  if (!isPulseActive.value) return '待机'
  return '脉冲中'
})

const pulseWidth = computed(() => {
  switch (props.phase) {
    case EXPERIMENT_PHASES.INTERMEDIATE_CHARGING:
      return '2.5 μs'
    case EXPERIMENT_PHASES.FINAL_CHARGING:
      return '1.8 μs'
    default:
      return '-- μs'
  }
})

const pulseFrequency = computed(() => {
  switch (props.phase) {
    case EXPERIMENT_PHASES.INTERMEDIATE_CHARGING:
      return '0.1 Hz'
    case EXPERIMENT_PHASES.FINAL_CHARGING:
      return '0.05 Hz'
    default:
      return '-- Hz'
  }
})

const pulseEnergy = computed(() => {
  const energy = (currentPulseValue.value * currentPulseValue.value) / (2 * 1000000) // 简化计算
  if (energy >= 1000) {
    return `${(energy / 1000).toFixed(1)} MJ`
  } else if (energy >= 1) {
    return `${energy.toFixed(1)} KJ`
  }
  return `${(energy * 1000).toFixed(0)} J`
})

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return
  
  chartInstance = echarts.init(chartContainer.value)
  
  const option = {
    backgroundColor: 'transparent',
    grid: {
      left: '8%',
      right: '8%',
      top: '10%',
      bottom: '15%'
    },
    xAxis: {
      type: 'value',
      name: '时间(μs)',
      nameTextStyle: { color: '#7C4DFF', fontSize: 10 },
      axisLine: { lineStyle: { color: '#7C4DFF60' } },
      axisTick: { lineStyle: { color: '#7C4DFF60' } },
      axisLabel: { 
        color: '#7C4DFFCC', 
        fontSize: 9,
        formatter: (value) => `${(value * 1000000).toFixed(1)}`
      },
      splitLine: { lineStyle: { color: '#7C4DFF20' } }
    },
    yAxis: {
      type: 'value',
      name: '电压(V)',
      nameTextStyle: { color: '#7C4DFF', fontSize: 10 },
      axisLine: { lineStyle: { color: '#7C4DFF60' } },
      axisTick: { lineStyle: { color: '#7C4DFF60' } },
      axisLabel: { 
        color: '#7C4DFFCC', 
        fontSize: 9,
        formatter: (value) => {
          if (value >= 1000000) return `${(value / 1000000).toFixed(1)}M`
          if (value >= 1000) return `${(value / 1000).toFixed(0)}K`
          return value.toFixed(0)
        }
      },
      splitLine: { lineStyle: { color: '#7C4DFF20' } }
    },
    series: [{
      type: 'line',
      data: [],
      smooth: false, // 脉冲波形不平滑
      lineStyle: {
        color: '#FFC400',
        width: 3,
        shadowColor: '#FFC400',
        shadowBlur: 10
      },
      itemStyle: {
        color: '#FFC400',
        borderColor: '#FFC400',
        borderWidth: 2
      },
      symbol: 'none',
      animation: true,
      animationDuration: 200
    }],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(10, 25, 46, 0.9)',
      borderColor: '#FFC400',
      textStyle: { color: '#fff' },
      formatter: (params) => {
        const point = params[0]
        return `
          <div style="padding: 5px;">
            <div style="color: #FFC400; font-weight: bold;">${pulseTitle.value}</div>
            <div>时间: ${(point.data[0] * 1000000).toFixed(2)}μs</div>
            <div>电压: ${formatVoltage(point.data[1])}</div>
          </div>
        `
      }
    }
  }
  
  chartInstance.setOption(option)
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance || !props.pulseData) return
  
  const chartData = props.pulseData.map(item => [
    parseFloat(item.time),
    parseFloat(item.value)
  ])
  
  chartInstance.setOption({
    series: [{
      data: chartData
    }]
  })
  
  // 如果有脉冲，添加闪烁效果
  if (isPulseActive.value) {
    setTimeout(() => {
      chartInstance?.setOption({
        series: [{
          lineStyle: {
            color: '#FF4444',
            shadowColor: '#FF4444',
            shadowBlur: 20
          }
        }]
      })
    }, 100)
    
    setTimeout(() => {
      chartInstance?.setOption({
        series: [{
          lineStyle: {
            color: '#FFC400',
            shadowColor: '#FFC400',
            shadowBlur: 10
          }
        }]
      })
    }, 300)
  }
}

// 监听数据变化
watch(() => props.pulseData, () => {
  updateChart()
}, { deep: true })

watch(() => props.phase, () => {
  if (chartInstance) {
    chartInstance.dispose()
    nextTick(() => {
      initChart()
      updateChart()
    })
  }
})

// 生命周期
onMounted(() => {
  nextTick(() => {
    initChart()
    updateChart()
  })
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
})
</script>

<style scoped>
.pulse-waveform {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: transparent;
  border: none;
  border-radius: 12px;
  padding: 15px;
}

.waveform-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(124, 77, 255, 0.3);
}

.waveform-title {
  font-size: 14px;
  font-weight: bold;
  color: #7C4DFF;
  margin: 0;
}

.pulse-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #666;
  transition: all 0.3s ease;
}

.pulse-indicator.active .indicator-dot {
  background: #FFC400;
  box-shadow: 0 0 15px #FFC400;
  animation: pulseGlow 0.5s ease-in-out infinite alternate;
}

@keyframes pulseGlow {
  0% { 
    box-shadow: 0 0 15px #FFC400;
    transform: scale(1);
  }
  100% { 
    box-shadow: 0 0 25px #FFC400;
    transform: scale(1.2);
  }
}

.indicator-text {
  font-size: 12px;
  color: #ccc;
}

.pulse-indicator.active .indicator-text {
  color: #FFC400;
  font-weight: bold;
}

.waveform-content {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
  flex: 1;
}

.pulse-value {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 100px;
}

.value-display {
  display: flex;
  align-items: baseline;
  gap: 5px;
}

.value-number {
  font-size: 20px;
  font-weight: bold;
  color: #FFC400;
  text-shadow: 0 0 10px rgba(255, 196, 0, 0.5);
  font-family: 'Courier New', monospace;
}

.value-unit {
  font-size: 12px;
  color: #ccc;
}

.value-label {
  font-size: 10px;
  color: #7C4DFF;
  margin-top: 5px;
  text-align: center;
}

.pulse-chart {
  flex: 1;
  min-height: 100px;
}

.pulse-parameters {
  display: flex;
  justify-content: space-between;
  gap: 15px;
  padding-top: 10px;
  border-top: 1px solid rgba(124, 77, 255, 0.3);
}

.param-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3px;
}

.param-label {
  font-size: 10px;
  color: #ccc;
}

.param-value {
  font-size: 11px;
  font-weight: bold;
  color: #18FFFF;
}

/* 脉冲激活时的整体发光效果 */
.pulse-waveform.active {
  border-color: #FFC400;
  box-shadow: 0 0 30px rgba(255, 196, 0, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .waveform-content {
    flex-direction: column;
    gap: 10px;
  }
  
  .pulse-parameters {
    flex-direction: column;
    gap: 8px;
  }
  
  .param-item {
    flex-direction: row;
    justify-content: space-between;
  }
}
</style>
