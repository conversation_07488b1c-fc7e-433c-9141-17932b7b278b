// Mock数据生成器
import { EXPERIMENT_PHASES, CAPACITOR_GROUPS } from '../config/experimentConfig.js'

// 生成波形数据的工具函数 - 支持进度参数
export function generateWaveform(duration, maxValue, waveType = 'linear', noise = 0.1, progress = 1) {
  const points = []
  const steps = Math.floor(duration / 100) // 每100ms一个数据点
  // 根据进度计算当前应该生成的点数
  const currentSteps = Math.floor(steps * progress)

  for (let i = 0; i <= currentSteps; i++) {
    const time = (i / steps) * (duration / 1000) // 转换为秒
    let value = 0

    switch (waveType) {
      case 'linear':
        value = (i / steps) * maxValue
        break
      case 'exponential':
        value = maxValue * (1 - Math.exp(-3 * i / steps))
        break
      case 'decay':
        value = maxValue * Math.exp(-3 * i / steps)
        break
      case 'pulse':
        value = i === Math.floor(steps * 0.1) ? maxValue : 0
        break
      case 'sine':
        value = maxValue * Math.sin(2 * Math.PI * i / steps)
        break
      case 'static':
        value = maxValue + (Math.random() - 0.5) * noise * maxValue
        break
    }

    // 添加噪声
    if (noise > 0 && waveType !== 'static') {
      value += (Math.random() - 0.5) * noise * maxValue
    }

    points.push({ time: parseFloat(time.toFixed(2)), value: Math.max(0, parseFloat(value.toFixed(2))) })
  }

  return points
}

// 生成自定义波形数据 - 用于特殊的放电过程
function generateCustomWaveform(duration, maxValue, waveType, progress = 1) {
  const points = []
  const steps = Math.floor(duration / 100)
  const currentSteps = Math.floor(steps * progress)

  for (let i = 0; i <= currentSteps; i++) {
    const time = (i / steps) * (duration / 1000)
    let value = 0

    switch (waveType) {
      case 'discharge_to_intermediate':
        // 一级电容向中储电容放电：从100%衰减到20%
        value = maxValue * (0.2 + 0.8 * Math.exp(-2 * i / steps))
        break
      case 'discharge_to_final':
        // 中储电容向风化电容放电：从100%衰减到15%
        value = maxValue * (0.15 + 0.85 * Math.exp(-2.5 * i / steps))
        break
      case 'discharge_current_to_intermediate':
        // 一级电容放电电流：随电压降低而降低，更快衰减
        value = maxValue * Math.exp(-3 * i / steps)
        break
      case 'discharge_current_to_final':
        // 中储电容放电电流：随电压降低而降低，更快衰减
        value = maxValue * Math.exp(-3.5 * i / steps)
        break
      case 'discharge_to_antenna':
        // 风化电容向天线放电：快速衰减到接近0
        value = maxValue * Math.exp(-4 * i / steps)
        break
      case 'discharge_current_to_antenna':
        // 风化电容向天线放电电流：非常快速衰减
        value = maxValue * Math.exp(-5 * i / steps)
        break
      default:
        value = maxValue * Math.exp(-3 * i / steps)
    }

    // 添加小幅噪声
    value += (Math.random() - 0.5) * 0.02 * maxValue

    points.push({ time: parseFloat(time.toFixed(2)), value: Math.max(0, parseFloat(value.toFixed(2))) })
  }

  return points
}

// 生成气压数据 - 只在准备阶段最后10秒充气
export function generatePressureData(phase, progress = 1) {
  switch (phase) {
    case EXPERIMENT_PHASES.PREPARATION:
      // 准备阶段：前10秒气压为0（未充气），后10秒充气到2MPa
      const points = []
      const steps = Math.floor(20000 / 100) // 200个点，每100ms一个
      const currentSteps = Math.floor(steps * progress)

      for (let i = 0; i <= currentSteps; i++) {
        const time = (i / steps) * 20 // 20秒总时长
        let value = 0

        if (time < 10) {
          // 前10秒：气压为0，只有微小的检测噪声
          value = (Math.random() - 0.5) * 1000 // 很小的噪声，接近0
        } else {
          // 后10秒：充气过程，指数增长到2MPa
          const chargeProgress = (time - 10) / 10 // 0-1
          value = 1900000 * (1 - Math.exp(-3 * chargeProgress))
          value += (Math.random() - 0.5) * 50000 // 充气过程的噪声
        }

        points.push({ time: parseFloat(time.toFixed(2)), value: Math.max(0, parseFloat(value.toFixed(2))) })
      }
      return points

    case EXPERIMENT_PHASES.PRIMARY_CHARGING:
    case EXPERIMENT_PHASES.INTERMEDIATE_CHARGING:
    case EXPERIMENT_PHASES.FINAL_CHARGING:
      // 后续阶段：保持2MPa，只是测量，有小幅波动
      return generateWaveform(
        phase === EXPERIMENT_PHASES.PRIMARY_CHARGING ? 15000 :
        phase === EXPERIMENT_PHASES.INTERMEDIATE_CHARGING ? 10000 : 8000,
        2000000, 'static', 0.02, progress
      )

    default:
      return generateWaveform(5000, 2000000, 'static', 0.02, progress)
  }
}

// 生成电压数据 - 考虑电容器间的能量转移
export function generateVoltageData(capacitorType, phase, progress = 1) {
  const config = CAPACITOR_GROUPS[capacitorType]

  switch (phase) {
    case EXPERIMENT_PHASES.PREPARATION:
      return generateWaveform(20000, 0, 'static', 0.01, progress)

    case EXPERIMENT_PHASES.PRIMARY_CHARGING:
      if (capacitorType === 'PRIMARY') {
        // 一级充能阶段：前7秒充电到最高电压，后面保持满电压状态
        const totalDuration = 16000 // 总持续时间16秒
        const chargingDuration = 7000 // 充电时间7秒
        const points = []
        const steps = Math.floor(totalDuration / 100)
        const currentSteps = Math.floor(steps * progress)
        const chargingSteps = Math.floor(chargingDuration / 100) // 7秒对应的步数

        for (let i = 0; i <= currentSteps; i++) {
          const time = (i / steps) * (totalDuration / 1000)
          let value = 0

          if (i <= chargingSteps) {
            // 前7秒：指数充电到最高电压
            const chargeProgress = i / chargingSteps
            value = config.maxVoltage * (1 - Math.exp(-3 * chargeProgress))
          } else {
            // 7秒后：保持满电压，只有小幅波动
            value = config.maxVoltage * (0.98 + 0.02 * Math.random())
          }

          // 添加充电过程的噪声
          if (i <= chargingSteps) {
            value += (Math.random() - 0.5) * 0.02 * config.maxVoltage
          } else {
            value += (Math.random() - 0.5) * 0.005 * config.maxVoltage
          }

          points.push({ time: parseFloat(time.toFixed(2)), value: Math.max(0, parseFloat(value.toFixed(2))) })
        }
        return points
      }
      return generateWaveform(16000, 0, 'static', 0.01, progress)

    case EXPERIMENT_PHASES.INTERMEDIATE_CHARGING:
      if (capacitorType === 'PRIMARY') {
        // 一级电容放电：从满电压衰减到约20%
        return generateCustomWaveform(10000, config.maxVoltage, 'discharge_to_intermediate', progress)
      } else if (capacitorType === 'INTERMEDIATE') {
        // 中储电容充电：从0充到满电压
        return generateWaveform(10000, config.maxVoltage, 'exponential', 0.02, progress)
      }
      return generateWaveform(10000, 0, 'static', 0.01, progress)

    case EXPERIMENT_PHASES.FINAL_CHARGING:
      if (capacitorType === 'PRIMARY') {
        // 一级电容保持低电压
        return generateWaveform(8000, config.maxVoltage * 0.2, 'static', 0.02, progress)
      } else if (capacitorType === 'INTERMEDIATE') {
        // 中储电容放电：从满电压衰减到约15%
        return generateCustomWaveform(8000, config.maxVoltage, 'discharge_to_final', progress)
      } else if (capacitorType === 'FINAL') {
        // 风化电容充电：从0充到满电压
        return generateWaveform(8000, config.maxVoltage, 'exponential', 0.02, progress)
      }
      return generateWaveform(8000, 0, 'static', 0.01, progress)

    case EXPERIMENT_PHASES.FIELD_ESTABLISHMENT:
      if (capacitorType === 'PRIMARY') {
        // 一级电容保持低电压
        return generateWaveform(5000, config.maxVoltage * 0.2, 'static', 0.02, progress)
      } else if (capacitorType === 'INTERMEDIATE') {
        // 中储电容保持低电压
        return generateWaveform(5000, config.maxVoltage * 0.15, 'static', 0.02, progress)
      } else if (capacitorType === 'FINAL') {
        // 风化电容放电到天线：快速衰减
        return generateCustomWaveform(5000, config.maxVoltage, 'discharge_to_antenna', progress)
      }
      return generateWaveform(5000, 0, 'static', 0.01, progress)

    default:
      return generateWaveform(5000, 0, 'static', 0.01, progress)
  }
}

// 生成电流数据 - 放电电流随电压降低而降低
export function generateCurrentData(capacitorType, phase, progress = 1) {
  const config = CAPACITOR_GROUPS[capacitorType]

  switch (phase) {
    case EXPERIMENT_PHASES.PREPARATION:
      return generateWaveform(20000, 0, 'static', 0.01, progress)

    case EXPERIMENT_PHASES.PRIMARY_CHARGING:
      if (capacitorType === 'PRIMARY') {
        // 一级充能阶段：前7秒充电电流逐渐减小，7秒后保持很小的维持电流
        const totalDuration = 16000 // 总持续时间16秒
        const chargingDuration = 7000 // 充电时间7秒
        const points = []
        const steps = Math.floor(totalDuration / 100)
        const currentSteps = Math.floor(steps * progress)
        const chargingSteps = Math.floor(chargingDuration / 100) // 7秒对应的步数

        for (let i = 0; i <= currentSteps; i++) {
          const time = (i / steps) * (totalDuration / 1000)
          let value = 0

          if (i <= chargingSteps) {
            // 前7秒：充电电流从最大值指数衰减
            const chargeProgress = i / chargingSteps
            value = config.maxCurrent * Math.exp(-2 * chargeProgress)
          } else {
            // 7秒后：保持很小的维持电流
            value = config.maxCurrent * 0.02 * (0.8 + 0.4 * Math.random())
          }

          // 添加电流噪声
          if (i <= chargingSteps) {
            value += (Math.random() - 0.5) * 0.05 * config.maxCurrent
          } else {
            value += (Math.random() - 0.5) * 0.01 * config.maxCurrent
          }

          points.push({ time: parseFloat(time.toFixed(2)), value: Math.max(0, parseFloat(value.toFixed(2))) })
        }
        return points
      }
      return generateWaveform(16000, 0, 'static', 0.01, progress)

    case EXPERIMENT_PHASES.INTERMEDIATE_CHARGING:
      if (capacitorType === 'PRIMARY') {
        // 一级电容放电电流：随电压降低而降低
        return generateCustomWaveform(10000, config.maxCurrent, 'discharge_current_to_intermediate', progress)
      } else if (capacitorType === 'INTERMEDIATE') {
        // 中储电容充电电流：开始大，逐渐减小
        return generateWaveform(10000, config.maxCurrent, 'decay', 0.05, progress)
      }
      return generateWaveform(10000, 0, 'static', 0.01, progress)

    case EXPERIMENT_PHASES.FINAL_CHARGING:
      if (capacitorType === 'PRIMARY') {
        // 一级电容基本无电流
        return generateWaveform(8000, config.maxCurrent * 0.1, 'static', 0.02, progress)
      } else if (capacitorType === 'INTERMEDIATE') {
        // 中储电容放电电流：随电压降低而降低
        return generateCustomWaveform(8000, config.maxCurrent, 'discharge_current_to_final', progress)
      } else if (capacitorType === 'FINAL') {
        // 风化电容充电电流：开始大，逐渐减小
        return generateWaveform(8000, config.maxCurrent, 'decay', 0.05, progress)
      }
      return generateWaveform(8000, 0, 'static', 0.01, progress)

    case EXPERIMENT_PHASES.FIELD_ESTABLISHMENT:
      if (capacitorType === 'PRIMARY') {
        // 一级电容基本无电流
        return generateWaveform(5000, config.maxCurrent * 0.05, 'static', 0.02, progress)
      } else if (capacitorType === 'INTERMEDIATE') {
        // 中储电容基本无电流
        return generateWaveform(5000, config.maxCurrent * 0.05, 'static', 0.02, progress)
      } else if (capacitorType === 'FINAL') {
        // 风化电容向天线放电电流：快速衰减
        return generateCustomWaveform(5000, config.maxCurrent, 'discharge_current_to_antenna', progress)
      }
      return generateWaveform(5000, 0, 'static', 0.01, progress)

    default:
      return generateWaveform(5000, 0, 'static', 0.01, progress)
  }
}

// 生成脉冲波形数据 - 开关导通时的瞬间脉冲
export function generatePulseData(phase, progress = 1) {
  switch (phase) {
    case EXPERIMENT_PHASES.PRIMARY_CHARGING:
      // 一级充能阶段：在7秒充电完成后产生开关脉冲
      return generateSwitchPulse(16000, 50000, 'primary_switch', progress)
    case EXPERIMENT_PHASES.INTERMEDIATE_CHARGING:
      // 中储充能阶段：开关在开始时导通，产生瞬间脉冲
      return generateSwitchPulse(10000, 100000, 'intermediate_switch', progress)
    case EXPERIMENT_PHASES.FINAL_CHARGING:
      // 风化充能阶段：开关在开始时导通，产生瞬间脉冲
      return generateSwitchPulse(8000, 10000000, 'final_switch', progress)
    default:
      return generateWaveform(5000, 0, 'static', 0.01, progress)
  }
}

// 生成开关脉冲数据 - 瞬间脉冲，不需要动态变化
function generateSwitchPulse(duration, maxValue, switchType, progress = 1) {
  const points = []
  const steps = Math.floor(duration / 100)
  const currentSteps = Math.floor(steps * progress)

  for (let i = 0; i <= currentSteps; i++) {
    const time = (i / steps) * (duration / 1000)
    let value = 0

    switch (switchType) {
      case 'primary_switch':
        // 一级充能开关：在7秒后产生脉冲
        const switchTime = 7.0 // 7秒
        const switchStep = Math.floor((switchTime * 1000) / 100) // 7秒对应的步数
        if (i >= switchStep && i <= switchStep + 2) {
          // 在7秒到7.2秒之间产生脉冲
          value = i === switchStep + 1 ? maxValue : 0
        }
        break
      case 'intermediate_switch':
        // 中储充能开关：在开始时导通，产生瞬间脉冲
        if (i <= 2) { // 前200ms内的脉冲
          value = i === 1 ? maxValue : 0 // 在第100ms时出现脉冲
        }
        break
      case 'final_switch':
        // 风化充能开关：在开始时导通，产生瞬间脉冲
        if (i <= 2) { // 前200ms内的脉冲
          value = i === 1 ? maxValue : 0 // 在第100ms时出现脉冲
        }
        break
    }

    // 脉冲不需要噪声，保持干净的信号
    points.push({ time: parseFloat(time.toFixed(2)), value: parseFloat(value.toFixed(2)) })
  }

  return points
}

// 生成电场强度数据（多通道）- 电场建立阶段的实时数据
export function generateFieldStrengthData(progress = 1) {
  // 电场建立阶段总持续时间18秒，第17秒开始生成实时数据
  const totalDuration = 18000 // 18秒
  const activationTime = 17000 // 17秒后开始激活
  const currentTime = progress * totalDuration // 当前时间（毫秒）
  
  const channels = [
    { name: 'A', maxValue: 850000, color: '#00F0FF', position: '北侧', height: '10m' },
    { name: 'B', maxValue: 920000, color: '#39FF14', position: '东侧', height: '10m' },
    { name: 'C', maxValue: 780000, color: '#18FFFF', position: '南侧', height: '10m' },
    { name: 'D', maxValue: 960000, color: '#7C4DFF', position: '西侧', height: '10m' },
    { name: 'E', maxValue: 890000, color: '#E91E63', position: '中心', height: '15m' },
    { name: 'F', maxValue: 820000, color: '#CCFF00', position: '中心', height: '5m' }
  ]

  // 判断是否已经到达激活时间
  const isActivated = currentTime >= activationTime

  return {
    // 实时监测数据
    realTimeMonitoring: {
      averageFieldStrength: isActivated ? (850 + Math.random() * 100).toFixed(1) + ' kV/m' : '等待激活...',
      maxFieldStrength: isActivated ? (960 + Math.random() * 50).toFixed(1) + ' kV/m' : '等待激活...',
      minFieldStrength: isActivated ? (780 + Math.random() * 30).toFixed(1) + ' kV/m' : '等待激活...',
      fieldUniformity: isActivated ? (92 + Math.random() * 5).toFixed(1) + '%' : '等待激活...',
      measurementTime: isActivated ? new Date().toLocaleTimeString('zh-CN') : '--:--:--',
      dataQuality: isActivated ? '优秀' : '等待中',
      calibrationStatus: isActivated ? '已校准' : '待激活'
    },

    // 通道数据
    channels: channels.map(channel => ({
      channel: channel.name,
      color: channel.color,
      position: channel.position,
      height: channel.height,
      currentValue: isActivated ? (channel.maxValue * (0.8 + 0.2 * Math.random()) / 1000).toFixed(1) : '0.0', // 转换为kV/m
      peakValue: isActivated ? (channel.maxValue / 1000).toFixed(1) : '0.0',
      averageValue: isActivated ? (channel.maxValue * 0.85 / 1000).toFixed(1) : '0.0',
      data: isActivated ? generateFieldWaveform(1000, channel.maxValue, (currentTime - activationTime) / 1000) : [],
      unit: 'kV/m',
      status: isActivated ? '正常' : '待激活',
      signalQuality: isActivated ? (95 + Math.random() * 5).toFixed(1) + '%' : '0%'
    })),

    // 环境参数
    environmentalData: {
      temperature: (23 + Math.random() * 2).toFixed(1) + '°C',
      humidity: (45 + Math.random() * 5).toFixed(1) + '%',
      pressure: '101.3 kPa',
      windSpeed: (2 + Math.random() * 3).toFixed(1) + ' m/s',
      windDirection: '东北',
      visibility: '> 10 km'
    },

    // 测量系统状态
    systemStatus: {
      sensorStatus: isActivated ? '全部正常' : '待激活',
      dataAcquisition: isActivated ? '正常' : '待激活',
      signalProcessing: isActivated ? '正常' : '待激活',
      calibrationDate: '2024-01-15',
      nextCalibration: '2024-07-15',
      accuracy: '±2%',
      resolution: '1 kV/m'
    }
  }
}

// 生成电场波形数据
function generateFieldWaveform(duration, maxValue, progress = 1) {
  const points = []
  const steps = Math.floor(duration / 100)
  const currentSteps = Math.floor(steps * progress)

  for (let i = 0; i <= currentSteps; i++) {
    const time = (i / steps) * (duration / 1000)
    // 电场强度随时间快速上升然后稳定，有高频振荡
    let value = maxValue * (1 - Math.exp(-5 * i / steps))
    // 添加高频振荡
    value += maxValue * 0.1 * Math.sin(20 * Math.PI * i / steps)
    // 添加噪声
    value += (Math.random() - 0.5) * 0.05 * maxValue

    points.push({ time: parseFloat(time.toFixed(2)), value: Math.max(0, parseFloat(value.toFixed(2))) })
  }

  return points
}

// 生成Marx建立波形数据 - 多通道可选择显示
export function generateMarxWaveform(progress = 1) {
  // 电场建立阶段总持续时间18秒，第17秒开始生成Marx波形
  const totalDuration = 18000 // 18秒
  const activationTime = 17000 // 17秒后开始激活
  const currentTime = progress * totalDuration // 当前时间（毫秒）
  
  const channels = [
    { name: '通道1', maxValue: 45000000, color: '#00F0FF', visible: true },
    { name: '通道2', maxValue: 52000000, color: '#39FF14', visible: true },
    { name: '通道3', maxValue: 48000000, color: '#18FFFF', visible: true },
    { name: '通道4', maxValue: 55000000, color: '#7C4DFF', visible: true },
    { name: '通道5', maxValue: 43000000, color: '#E91E63', visible: false },
    { name: '通道6', maxValue: 49000000, color: '#CCFF00', visible: false },
    { name: '通道7', maxValue: 51000000, color: '#00FFFF', visible: false },
    { name: '通道8', maxValue: 47000000, color: '#8A2BE2', visible: false }
  ]

  // 判断是否已经到达激活时间
  const isActivated = currentTime >= activationTime

  return channels.map(channel => ({
    channel: channel.name,
    color: channel.color,
    visible: channel.visible,
    data: isActivated ? generateMarxChannelWaveform(1000, channel.maxValue, (currentTime - activationTime) / 1000) : [],
    unit: 'V',
    status: isActivated ? '正常' : '待激活',
    currentValue: isActivated ? (channel.maxValue * (0.7 + 0.3 * Math.random()) / 1000000).toFixed(1) + ' MV' : '0.0 MV',
    peakValue: isActivated ? (channel.maxValue / 1000000).toFixed(1) + ' MV' : '0.0 MV'
  }))
}

// 生成Marx通道波形数据
function generateMarxChannelWaveform(duration, maxValue, progress = 1) {
  const points = []
  const steps = Math.floor(duration / 100)
  const currentSteps = Math.floor(steps * progress)

  for (let i = 0; i <= currentSteps; i++) {
    const time = (i / steps) * (duration / 1000)
    // Marx发生器的典型波形：快速上升，然后振荡衰减
    let value = 0

    if (i < steps * 0.1) {
      // 前10%时间快速上升
      value = maxValue * (i / (steps * 0.1))
    } else {
      // 后90%时间振荡衰减
      const decayTime = (i - steps * 0.1) / (steps * 0.9)
      value = maxValue * Math.exp(-2 * decayTime) * (1 + 0.3 * Math.sin(10 * Math.PI * decayTime))
    }

    // 添加小幅噪声
    value += (Math.random() - 0.5) * 0.02 * maxValue

    points.push({ time: parseFloat(time.toFixed(2)), value: Math.max(0, parseFloat(value.toFixed(2))) })
  }

  return points
}

// 生成信息报告摘要 - 电场建立阶段的详细报告
export function generateReportSummary() {
  const currentTime = new Date()
  const experimentId = `EXP-${currentTime.getFullYear()}${(currentTime.getMonth()+1).toString().padStart(2,'0')}${currentTime.getDate().toString().padStart(2,'0')}-${Math.floor(Math.random()*1000).toString().padStart(3,'0')}`

  return {
    // 实验基本信息
    experimentInfo: {
      experimentId: experimentId,
      experimentDate: currentTime.toLocaleDateString('zh-CN'),
      experimentTime: currentTime.toLocaleTimeString('zh-CN'),
      operator: '张工程师',
      location: '高压实验室A区',
      weather: '晴朗，湿度45%',
      temperature: '23°C'
    },

    // 电场参数
    fieldParameters: {
      direction: '垂直向上',
      area: '1.2 m²',
      riseTime: '2.3 μs',
      pulseWidth: '15.7 μs',
      amplitude: '85.6 MV/m',
      peakValue: '92.1 MV/m',
      peakTime: '8.9 μs',
      fallTime: '12.4 μs',
      frequency: '50 Hz',
      waveformType: '双指数脉冲'
    },

    // 设备状态
    equipmentStatus: {
      primaryCapacitor: '正常',
      intermediateCapacitor: '正常',
      finalCapacitor: '正常',
      switchSystem: '正常',
      antennaSystem: '正常',
      measurementSystem: '正常',
      safetySystem: '正常'
    },

    // 能量数据
    energyData: {
      totalEnergyStored: '2.45 MJ',
      energyReleased: (89 + Math.random() * 5).toFixed(1) + '%',
      energyEfficiency: '94.2%',
      powerPeak: '1.2 GW',
      energyLoss: '5.8%'
    },

    // 实时监测数据
    realTimeData: {
      currentFieldStrength: (850 + Math.random() * 100).toFixed(1) + ' kV/m',
      antennaVoltage: (45 + Math.random() * 10).toFixed(1) + ' MV',
      antennaCurrent: (12 + Math.random() * 3).toFixed(1) + ' kA',
      chamberPressure: '2.0 MPa',
      gasType: 'SF4',
      experimentStatus: '电场建立中',
      dataQuality: '优秀',
      signalNoiseRatio: '45 dB'
    },

    // 安全监测
    safetyMonitoring: {
      radiationLevel: '正常',
      electricalSafety: '安全',
      mechanicalSafety: '安全',
      emergencySystem: '就绪',
      personnelSafety: '安全',
      environmentalImpact: '无影响'
    },

    // 数据采集
    dataAcquisition: {
      samplingRate: '1 GS/s',
      recordLength: '10 ms',
      channels: '8通道',
      resolution: '12位',
      bandwidth: '500 MHz',
      triggerMode: '外部触发'
    },

    // 实验结果评估
    resultEvaluation: {
      experimentSuccess: true,
      dataValidity: '有效',
      repeatability: '良好',
      accuracy: '±2%',
      precision: '±1%',
      overallRating: '优秀'
    },

    // 备注信息
    notes: {
      technicalNotes: '电场建立过程稳定，波形质量良好',
      operatorNotes: '实验按计划顺利进行，所有参数正常',
      maintenanceNotes: '设备运行正常，无异常情况',
      nextSteps: '数据分析和报告生成'
    }
  }
}
