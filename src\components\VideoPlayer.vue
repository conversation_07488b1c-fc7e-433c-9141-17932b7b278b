<template>
  <div class="video-player">
    <div class="video-header">
      <h3 class="video-title">{{ videoTitle }}</h3>
      <div class="video-status">
        <span class="status-indicator" :class="statusClass"></span>
        <span class="status-text">{{ statusText }}</span>
      </div>
    </div>
    
    <div class="video-container" ref="videoContainer">
      <video 
        ref="videoElement"
        :src="videoPath"
        :muted="true"
        class="video-element"
        @loadstart="onLoadStart"
        @loadeddata="onLoadedData"
        @error="onError"
        @timeupdate="onTimeUpdate"
        @ended="onEnded"
      ></video>
      
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">加载中...</div>
      </div>
      
      <!-- 错误状态 -->
      <div v-if="hasError" class="error-overlay">
        <div class="error-icon">⚠️</div>
        <div class="error-text">视频加载失败</div>
        <div class="error-description">{{ errorMessage }}</div>
      </div>
    </div>
    
    <!-- 视频信息 -->
    <div class="video-info">
      <div class="info-item">
        <span class="info-label">阶段:</span>
        <span class="info-value">{{ phaseDisplayName }}</span>
      </div>
      <div class="info-item" v-if="videoDuration">
        <span class="info-label">时长:</span>
        <span class="info-value">{{ formatDuration(videoDuration) }}</span>
      </div>
      <div class="info-item" v-if="currentTime">
        <span class="info-label">时间:</span>
        <span class="info-value">{{ formatTime(currentTime) }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">状态:</span>
        <span class="info-value" :class="statusClass">{{ statusText }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { EXPERIMENT_PHASES, PHASE_CONFIG, SINGLE_VIDEO_PATH } from '../config/experimentConfig.js'

// Props
const props = defineProps({
  isPlaying: {
    type: Boolean,
    default: false
  },
  phase: {
    type: String,
    required: true
  }
})

// 响应式数据
const videoElement = ref(null)
const videoContainer = ref(null)
const isLoading = ref(false)
const hasError = ref(false)
const errorMessage = ref('')
const videoDuration = ref(0)
const currentTime = ref(0)
const isVideoReady = ref(false)

// 固定使用单个视频文件
const videoPath = SINGLE_VIDEO_PATH

// 计算属性
const videoTitle = computed(() => {
  return PHASE_CONFIG[props.phase]?.name || '实验视频'
})

const phaseDisplayName = computed(() => {
  return PHASE_CONFIG[props.phase]?.name || '未知阶段'
})

const statusText = computed(() => {
  if (hasError.value) return '错误'
  if (isLoading.value) return '加载中'
  if (props.isPlaying) return '播放中'
  return '待机'
})

const statusClass = computed(() => {
  if (hasError.value) return 'status-error'
  if (isLoading.value) return 'status-loading'
  if (props.isPlaying) return 'status-playing'
  return 'status-idle'
})

// 获取当前阶段的视频时间范围
const getPhaseTimeRange = () => {
  const config = PHASE_CONFIG[props.phase]
  return {
    startTime: config?.videoStartTime || 0,
    endTime: config?.videoEndTime || 60
  }
}

// 方法
const formatDuration = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const formatTime = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

// 视频事件处理
const onLoadStart = () => {
  isLoading.value = true
  hasError.value = false
  isVideoReady.value = false
}

const onLoadedData = () => {
  isLoading.value = false
  isVideoReady.value = true
  if (videoElement.value) {
    videoDuration.value = videoElement.value.duration
    // 设置视频到当前阶段的开始时间
    const { startTime } = getPhaseTimeRange()
    videoElement.value.currentTime = startTime
  }
}

const onError = (event) => {
  isLoading.value = false
  hasError.value = true
  isVideoReady.value = false
  errorMessage.value = '无法加载视频文件'
  console.error('Video error:', event)
}

const onTimeUpdate = () => {
  if (videoElement.value) {
    currentTime.value = videoElement.value.currentTime
    
    // 检查是否到达当前阶段的结束时间
    const { endTime } = getPhaseTimeRange()
    if (currentTime.value >= endTime && props.isPlaying) {
      pauseVideo()
      // 可以发送阶段完成事件
      // emit('phase-complete')
    }
  }
}

const onEnded = () => {
  // 视频结束处理
  console.log('Video ended')
}

// 控制视频播放
const playVideo = () => {
  if (videoElement.value && !hasError.value && isVideoReady.value) {
    // 确保视频在正确的时间点开始播放
    const { startTime, endTime } = getPhaseTimeRange()
    
    // 如果当前时间不在阶段范围内，设置到开始时间
    if (videoElement.value.currentTime < startTime || videoElement.value.currentTime >= endTime) {
      videoElement.value.currentTime = startTime
    }
    
    videoElement.value.play().catch(err => {
      console.error('Play error:', err)
    })
  }
}

const pauseVideo = () => {
  if (videoElement.value) {
    videoElement.value.pause()
  }
}

const seekToPhaseStart = () => {
  if (videoElement.value && isVideoReady.value) {
    const { startTime } = getPhaseTimeRange()
    videoElement.value.currentTime = startTime
  }
}

// 监听播放状态变化
watch(() => props.isPlaying, (newVal) => {
  if (newVal) {
    playVideo()
  } else {
    pauseVideo()
  }
})

// 监听阶段变化
watch(() => props.phase, () => {
  // 阶段变化时，跳转到新阶段的开始时间
  seekToPhaseStart()
  
  // 如果正在播放，继续播放
  if (props.isPlaying) {
    setTimeout(() => {
      playVideo()
    }, 100) // 给一点时间让视频跳转完成
  }
})

// 生命周期
onMounted(() => {
  // 初始化
})

onUnmounted(() => {
  if (videoElement.value) {
    videoElement.value.pause()
  }
})
</script>

<style scoped>
.video-player {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: transparent;
  border: none;
  border-radius: 12px;
  overflow: hidden;
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: rgba(0, 240, 255, 0.1);
  border-bottom: 1px solid rgba(0, 240, 255, 0.3);
}

.video-title {
  font-size: 16px;
  font-weight: bold;
  color: #00F0FF;
  margin: 0;
}

.video-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #666;
}

.status-indicator.status-playing {
  background: #39FF14;
  box-shadow: 0 0 10px #39FF14;
  animation: pulse 1s ease-in-out infinite alternate;
}

.status-indicator.status-loading {
  background: #FFC400;
  animation: blink 1s ease-in-out infinite;
}

.status-indicator.status-error {
  background: #FF4444;
}

@keyframes pulse {
  0% { opacity: 0.5; }
  100% { opacity: 1; }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

.status-text {
  font-size: 12px;
  color: #ccc;
}

.video-container {
  flex: 1;
  position: relative;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.video-element {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 240, 255, 0.3);
  border-top: 3px solid #00F0FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text,
.error-text {
  font-size: 16px;
  margin-bottom: 5px;
}

.error-icon {
  font-size: 32px;
  margin-bottom: 10px;
}

.error-description {
  font-size: 12px;
  color: #ccc;
}

.video-info {
  display: flex;
  justify-content: space-between;
  padding: 10px 20px;
  background: rgba(0, 240, 255, 0.05);
  border-top: 1px solid rgba(0, 240, 255, 0.3);
}

.info-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.info-label {
  font-size: 11px;
  color: #ccc;
}

.info-value {
  font-size: 11px;
  font-weight: bold;
  color: #18FFFF;
}

.info-value.status-playing {
  color: #39FF14;
}

.info-value.status-error {
  color: #FF4444;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-info {
    flex-direction: column;
    gap: 5px;
  }
}
</style>
