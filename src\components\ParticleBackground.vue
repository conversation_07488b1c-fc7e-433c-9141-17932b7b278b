<template>
  <div class="particle-background">
    <div class="particles-container" ref="particlesContainer">
      <div 
        v-for="particle in particles" 
        :key="particle.id"
        class="particle"
        :style="particle.style"
      ></div>
    </div>
    
    <!-- 网格线背景 -->
    <div class="grid-background"></div>
    
    <!-- 流动线条 -->
    <div class="flowing-lines">
      <div class="line line-1"></div>
      <div class="line line-2"></div>
      <div class="line line-3"></div>
    </div>

    <!-- 渐变光斑 -->
    <div class="gradient-spots">
      <div class="spot spot-1"></div>
      <div class="spot spot-2"></div>
      <div class="spot spot-3"></div>
      <div class="spot spot-4"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

const particlesContainer = ref(null)
const particles = ref([])
let animationId = null

// 创建粒子
const createParticle = (id) => {
  return {
    id,
    x: Math.random() * window.innerWidth,
    y: Math.random() * window.innerHeight,
    vx: (Math.random() - 0.5) * 0.3,
    vy: (Math.random() - 0.5) * 0.3,
    size: Math.random() * 2 + 0.5,
    opacity: Math.random() * 0.2 + 0.1,
    color: ['#00F0FF', '#39FF14', '#18FFFF', '#7C4DFF', '#E91E63', '#CCFF00'][Math.floor(Math.random() * 6)],
    style: {}
  }
}

// 初始化粒子
const initParticles = () => {
  particles.value = []
  for (let i = 0; i < 30; i++) {
    particles.value.push(createParticle(i))
  }
}

// 更新粒子位置
const updateParticles = () => {
  particles.value.forEach(particle => {
    particle.x += particle.vx
    particle.y += particle.vy
    
    // 边界检测
    if (particle.x < 0 || particle.x > window.innerWidth) {
      particle.vx *= -1
    }
    if (particle.y < 0 || particle.y > window.innerHeight) {
      particle.vy *= -1
    }
    
    // 更新样式
    particle.style = {
      left: `${particle.x}px`,
      top: `${particle.y}px`,
      width: `${particle.size}px`,
      height: `${particle.size}px`,
      backgroundColor: particle.color,
      opacity: particle.opacity,
      boxShadow: `0 0 ${particle.size * 2}px ${particle.color}`
    }
  })
}

// 动画循环
const animate = () => {
  updateParticles()
  animationId = requestAnimationFrame(animate)
}

onMounted(() => {
  initParticles()
  animate()
})

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
})
</script>

<style scoped>
.particle-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.particles-container {
  position: absolute;
  width: 100%;
  height: 100%;
}

.particle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
  animation: pulse 2s ease-in-out infinite alternate;
}

@keyframes pulse {
  0% { transform: scale(1); }
  100% { transform: scale(1.2); }
}

.grid-background {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(0, 240, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 240, 255, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

.flowing-lines {
  position: absolute;
  width: 100%;
  height: 100%;
}

.line {
  position: absolute;
  height: 2px;
  background: linear-gradient(90deg, transparent, #00F0FF, transparent);
  animation: flowRight 8s linear infinite;
}

.line-1 {
  top: 20%;
  width: 300px;
  animation-delay: 0s;
}

.line-2 {
  top: 50%;
  width: 400px;
  animation-delay: 2s;
}

.line-3 {
  top: 80%;
  width: 250px;
  animation-delay: 4s;
}

@keyframes flowRight {
  0% {
    left: -400px;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}

/* 渐变光斑 */
.gradient-spots {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.spot {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  animation: spotFloat 15s ease-in-out infinite;
}

.spot-1 {
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(0,240,255,0.05) 0%, transparent 70%);
  top: 10%;
  left: 20%;
  animation-delay: 0s;
}

.spot-2 {
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(57,255,20,0.04) 0%, transparent 70%);
  top: 60%;
  right: 15%;
  animation-delay: 5s;
}

.spot-3 {
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, rgba(124,77,255,0.06) 0%, transparent 70%);
  bottom: 20%;
  left: 10%;
  animation-delay: 10s;
}

.spot-4 {
  width: 350px;
  height: 350px;
  background: radial-gradient(circle, rgba(233,30,99,0.03) 0%, transparent 70%);
  top: 30%;
  right: 40%;
  animation-delay: 7s;
}

@keyframes spotFloat {
  0%, 100% {
    transform: translate(0, 0) scale(1);
    opacity: 0.3;
  }
  25% {
    transform: translate(30px, -20px) scale(1.1);
    opacity: 0.5;
  }
  50% {
    transform: translate(-20px, 30px) scale(0.9);
    opacity: 0.4;
  }
  75% {
    transform: translate(40px, 10px) scale(1.05);
    opacity: 0.6;
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .grid-background {
    background-size: 30px 30px;
  }

  .line {
    height: 1px;
  }

  .spot {
    filter: blur(20px);
  }

  .spot-1, .spot-2, .spot-3, .spot-4 {
    width: 200px;
    height: 200px;
  }
}
</style>
