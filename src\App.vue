<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import ExperimentDashboard from './components/ExperimentDashboard.vue'
import ParticleBackground from './components/ParticleBackground.vue'
import TestComponent from './components/TestComponent.vue'

// 响应式状态
const isFullscreen = ref(false)

// 全屏切换
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    document.exitFullscreen()
    isFullscreen.value = false
  }
}

// 监听全屏状态变化
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
}

onMounted(() => {
  document.addEventListener('fullscreenchange', handleFullscreenChange)
})

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
})
</script>

<template>
  <div id="app" class="app-container">
    <!-- 粒子背景 -->
    <ParticleBackground />

    <!-- 主要内容 -->
    <div class="main-content">
      <!-- 顶部标题栏 -->
      <header class="app-header">
        <div class="header-left">
          <h1 class="app-title">电磁脉冲实验过程展示系统</h1>
          <div class="subtitle">Electromagnetic Pulse Experiment Display System</div>
        </div>
        <div class="header-right">
          <button @click="toggleFullscreen" class="fullscreen-btn">
            <span v-if="!isFullscreen">⛶</span>
            <span v-else>⛷</span>
          </button>
        </div>
      </header>

      <!-- 实验仪表板 -->
      <ExperimentDashboard />
    </div>
  </div>
</template>

<style scoped>
.app-container {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0A192E 0%, #112240 50%, #0A192E 100%);
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.main-content {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background: rgba(10, 25, 46, 0.95);
  border-bottom: 1px solid #00F0FF;
  box-shadow: 0 2px 15px rgba(0, 240, 255, 0.3);
  height: 50px;
  flex-shrink: 0;
  width: 100%;
  box-sizing: border-box;
  position: relative;
  left: 0;
  top: 0;
}

.header-left {
  display: flex;
  flex-direction: column;
}

.app-title {
  font-size: 22px;
  font-weight: bold;
  color: #00F0FF;
  margin: 0;
  text-shadow: 0 0 10px rgba(0, 240, 255, 0.5);
  letter-spacing: 1px;
}

.subtitle {
  font-size: 12px;
  color: #39FF14;
  margin-top: 2px;
  letter-spacing: 1px;
}

.header-right {
  display: flex;
  align-items: center;
}

.fullscreen-btn {
  background: linear-gradient(45deg, #00F0FF, #39FF14);
  border: none;
  color: #0A192E;
  font-size: 16px;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 0 15px rgba(0, 240, 255, 0.4);
}

.fullscreen-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 0 25px rgba(0, 240, 255, 0.6);
}
</style>
