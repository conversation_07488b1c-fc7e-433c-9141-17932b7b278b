<template>
  <span class="animated-number" :style="{ color: color }">
    {{ displayValue }}
  </span>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'

const props = defineProps({
  value: {
    type: Number,
    default: 0
  },
  duration: {
    type: Number,
    default: 1000
  },
  decimals: {
    type: Number,
    default: 1
  },
  color: {
    type: String,
    default: '#39FF14'
  }
})

const displayValue = ref(0)
let animationId = null

const animateToValue = (targetValue) => {
  const startValue = displayValue.value
  const difference = targetValue - startValue
  const startTime = Date.now()
  
  const animate = () => {
    const elapsed = Date.now() - startTime
    const progress = Math.min(elapsed / props.duration, 1)
    
    // 使用缓动函数
    const easeOutCubic = 1 - Math.pow(1 - progress, 3)
    
    displayValue.value = startValue + (difference * easeOutCubic)
    
    if (progress < 1) {
      animationId = requestAnimationFrame(animate)
    } else {
      displayValue.value = targetValue
    }
  }
  
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
  
  animate()
}

watch(() => props.value, (newValue) => {
  animateToValue(newValue)
}, { immediate: true })

onMounted(() => {
  displayValue.value = props.value
})
</script>

<style scoped>
.animated-number {
  font-weight: bold;
  text-shadow: 0 0 8px currentColor;
  transition: all 0.3s ease;
  display: inline-block;
}

.animated-number:hover {
  transform: scale(1.05);
  filter: brightness(1.2);
}
</style>
