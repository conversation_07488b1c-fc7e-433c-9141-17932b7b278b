// 测试电场建立阶段和气压修复
import { 
  generateVoltageData, 
  generateCurrentData, 
  generatePressureData,
  generateFieldStrengthData,
  generateMarxWaveform,
  generateReportSummary
} from './src/data/mockData.js'

// 模拟实验阶段常量
const EXPERIMENT_PHASES = {
  PREPARATION: 'preparation',
  PRIMARY_CHARGING: 'primary_charging',
  INTERMEDIATE_CHARGING: 'intermediate_charging',
  FINAL_CHARGING: 'final_charging',
  FIELD_ESTABLISHMENT: 'field_establishment'
}

console.log('测试电场建立阶段和气压修复...\n')

// 1. 测试修复后的气压数据
console.log('=== 测试修复后的气压数据 ===')
const preparationPressure = generatePressureData(EXPERIMENT_PHASES.PREPARATION, 1)
console.log(`准备阶段气压数据点数: ${preparationPressure.length}`)

// 检查前10秒（应该接近0）
const earlyPressure = preparationPressure.filter(p => parseFloat(p.time) < 10)
const avgEarlyPressure = earlyPressure.reduce((sum, p) => sum + parseFloat(p.value), 0) / earlyPressure.length
console.log(`前10秒平均气压: ${avgEarlyPressure.toFixed(0)}Pa (应该接近0)`)

// 检查后10秒（应该上升到2MPa）
const latePressure = preparationPressure.filter(p => parseFloat(p.time) >= 15)
const avgLatePressure = latePressure.reduce((sum, p) => sum + parseFloat(p.value), 0) / latePressure.length
console.log(`后5秒平均气压: ${avgLatePressure.toFixed(0)}Pa (应该接近2MPa)`)

// 2. 测试电场建立阶段的电压数据
console.log('\n=== 测试电场建立阶段电压数据 ===')
const primaryVoltageField = generateVoltageData('PRIMARY', EXPERIMENT_PHASES.FIELD_ESTABLISHMENT, 1)
const intermediateVoltageField = generateVoltageData('INTERMEDIATE', EXPERIMENT_PHASES.FIELD_ESTABLISHMENT, 1)
const finalVoltageField = generateVoltageData('FINAL', EXPERIMENT_PHASES.FIELD_ESTABLISHMENT, 1)

console.log(`一级电容电压: ${primaryVoltageField[0].value}V -> ${primaryVoltageField[primaryVoltageField.length-1].value}V (应该保持低压)`)
console.log(`中储电容电压: ${intermediateVoltageField[0].value}V -> ${intermediateVoltageField[intermediateVoltageField.length-1].value}V (应该保持低压)`)
console.log(`风化电容电压: ${finalVoltageField[0].value}V -> ${finalVoltageField[finalVoltageField.length-1].value}V (应该快速衰减到天线)`)

// 3. 测试电场建立阶段的电流数据
console.log('\n=== 测试电场建立阶段电流数据 ===')
const primaryCurrentField = generateCurrentData('PRIMARY', EXPERIMENT_PHASES.FIELD_ESTABLISHMENT, 1)
const intermediateCurrentField = generateCurrentData('INTERMEDIATE', EXPERIMENT_PHASES.FIELD_ESTABLISHMENT, 1)
const finalCurrentField = generateCurrentData('FINAL', EXPERIMENT_PHASES.FIELD_ESTABLISHMENT, 1)

console.log(`一级电容电流: ${primaryCurrentField[0].value}A -> ${primaryCurrentField[primaryCurrentField.length-1].value}A (应该基本无电流)`)
console.log(`中储电容电流: ${intermediateCurrentField[0].value}A -> ${intermediateCurrentField[intermediateCurrentField.length-1].value}A (应该基本无电流)`)
console.log(`风化电容电流: ${finalCurrentField[0].value}A -> ${finalCurrentField[finalCurrentField.length-1].value}A (应该快速衰减)`)

// 4. 测试电场强度数据
console.log('\n=== 测试电场强度数据 ===')
const fieldStrengthData = generateFieldStrengthData(1)
console.log(`电场强度通道数: ${fieldStrengthData.length}`)
fieldStrengthData.forEach(channel => {
  console.log(`通道${channel.channel}: 当前值=${channel.currentValue.toFixed(0)}${channel.unit}, 数据点数=${channel.data.length}, 颜色=${channel.color}`)
})

// 5. 测试Marx建立波形数据
console.log('\n=== 测试Marx建立波形数据 ===')
const marxData = generateMarxWaveform(1)
console.log(`Marx波形通道数: ${marxData.length}`)
const visibleChannels = marxData.filter(ch => ch.visible)
const hiddenChannels = marxData.filter(ch => !ch.visible)
console.log(`默认显示通道数: ${visibleChannels.length}`)
console.log(`默认隐藏通道数: ${hiddenChannels.length}`)

visibleChannels.forEach(channel => {
  const maxValue = Math.max(...channel.data.map(p => parseFloat(p.value)))
  console.log(`${channel.channel}: 最大值=${(maxValue/1000000).toFixed(1)}MV, 颜色=${channel.color}`)
})

// 6. 测试信息报告摘要
console.log('\n=== 测试信息报告摘要 ===')
const reportSummary = generateReportSummary()
console.log('基础报告数据:')
Object.entries(reportSummary).forEach(([key, value]) => {
  if (key !== 'realTimeData') {
    console.log(`  ${key}: ${value}`)
  }
})

console.log('实时数据:')
Object.entries(reportSummary.realTimeData).forEach(([key, value]) => {
  console.log(`  ${key}: ${value}`)
})

console.log('\n测试完成！')
console.log('修复和优化总结：')
console.log('✓ 气压数据：前10秒接近0，后10秒充气到2MPa')
console.log('✓ 电场建立阶段：风化电容向天线放电，电压和电流快速衰减')
console.log('✓ 电场强度：多通道实时数据，包含当前值和波形')
console.log('✓ Marx波形：8个通道，默认显示4个，可选择显示')
console.log('✓ 信息报告：包含基础数据和实时更新数据')
