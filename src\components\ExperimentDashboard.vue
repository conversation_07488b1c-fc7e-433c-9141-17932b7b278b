<template>
  <div class="experiment-dashboard">
    <!-- 控制面板 -->
    <ControlPanel
      :current-phase="currentPhase"
      :is-playing="isPlaying"
      :progress="progress"
      @play="startExperiment"
      @pause="pauseExperiment"
      @reset="resetExperiment"
      @phase-change="changePhase"
    />

    <!-- 主要实验界面 -->
    <div class="dashboard-layout">
      <!-- 左侧栏 -->
      <div class="left-column">
        <!-- 电场建立阶段显示电场强度，其他阶段显示电压 -->
        <div v-if="currentPhase === 'field_establishment'" class="section-item">
          <FieldStrengthDisplay :field-data="fieldStrengthData" />
        </div>
        <template v-else>
          <div class="section-item">
            <WaveformChart
              title="中储电压波形"
              :data="voltageData.primary"
              :color="colors.primary"
              unit="V"
              :max-value="1000000"
            />
          </div>
          <div class="section-item">
            <WaveformChart
              title="峰化电压波形"
              :data="voltageData.intermediate"
              :color="colors.secondary"
              unit="V"
              :max-value="30000000"
            />
          </div>
          <div class="section-item">
            <WaveformChart
              title="场波形"
              :data="voltageData.final"
              :color="colors.accent"
              unit="V"
              :max-value="100000000"
            />
          </div>
        </template>
      </div>

      <!-- 中间栏 -->
      <div class="center-column">
        <!-- 中间上部：视频播放区域 -->
        <div class="center-top section-item">
          <VideoPlayer
            :is-playing="isPlaying"
            :phase="currentPhase"
          />
        </div>

        <!-- 中间下部：根据阶段显示不同内容 -->
        <div class="center-bottom">
          <!-- 电场建立阶段显示Marx波形 -->
          <div v-if="currentPhase === 'field_establishment'" class="center-bottom-full section-item">
            <MarxWaveform :marx-data="marxData" />
          </div>
          <!-- 准备阶段只显示脉冲波形 -->
          <template v-else-if="currentPhase === 'preparation'">
            <div class="center-bottom-full section-item">
              <PulseWaveform :pulse-data="pulseData" :phase="currentPhase" />
            </div>
          </template>
          <!-- 其他阶段只显示脉冲波形 -->
          <template v-else>
            <div class="center-bottom-full section-item">
              <PulseWaveform :pulse-data="pulseData" :phase="currentPhase" />
            </div>
          </template>
        </div>
      </div>

      <!-- 右侧栏 -->
      <div class="right-column">
        <!-- 电场建立阶段显示实验报告，其他阶段显示设备状态 -->
        <div v-if="currentPhase === 'field_establishment'" class="section-item">
          <ReportSummary :report-data="reportSummary" />
        </div>
        <div v-else class="section-item device-status-container">
          <DeviceStatusDisplay
            :current-phase="currentPhase"
            :progress="progress"
            :voltage-data="voltageData"
            :current-data="currentData"
            :is-playing="isPlaying"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { EXPERIMENT_PHASES, PHASE_CONFIG, COLORS } from '../config/experimentConfig.js'
import {
  generateVoltageData,
  generateCurrentData,
  generatePulseData,
  generateFieldStrengthData,
  generateMarxWaveform,
  generateReportSummary
} from '../data/mockData.js'

// 导入子组件
import ControlPanel from './ControlPanel.vue'
import WaveformChart from './WaveformChart.vue'
import VideoPlayer from './VideoPlayer.vue'

import PulseWaveform from './PulseWaveform.vue'
import FieldStrengthDisplay from './FieldStrengthDisplay.vue'
import ReportSummary from './ReportSummary.vue'
import MarxWaveform from './MarxWaveform.vue'
import DeviceStatusDisplay from './DeviceStatusDisplay.vue'

// 响应式状态
const currentPhase = ref(EXPERIMENT_PHASES.PREPARATION)
const isPlaying = ref(false)
const progress = ref(0)
const colors = COLORS

// 数据状态 - 累积历史数据
const voltageData = reactive({
  primary: [],
  intermediate: [],
  final: []
})

const currentData = reactive({
  primary: [],
  intermediate: [],
  final: []
})

// 记录当前阶段和进度，用于数据累积
const lastPhase = ref(null)
const lastProgress = ref(0)


const pulseData = ref([])
const fieldStrengthData = ref([])
const marxData = ref([])
const reportSummary = ref({})

// 计算属性
// 注释掉不再需要的currentVideoPath
// const currentVideoPath = computed(() => {
//   return PHASE_CONFIG[currentPhase.value]?.videoPath || null
// })

// 定时器
let phaseTimer = null
let dataUpdateTimer = null

// 实验控制方法
const startExperiment = () => {
  console.log('startExperiment called')
  console.log('Current phase:', currentPhase.value)
  console.log('Is playing before:', isPlaying.value)
  
  isPlaying.value = true
  console.log('Is playing after:', isPlaying.value)
  
  updatePhaseData()
  startPhaseTimer()
  
  console.log('startExperiment completed')
}

const pauseExperiment = () => {
  isPlaying.value = false
  clearTimers()
}

const resetExperiment = () => {
  isPlaying.value = false
  currentPhase.value = EXPERIMENT_PHASES.PREPARATION
  progress.value = 0

  // 清空累积的历史数据
  voltageData.primary = []
  voltageData.intermediate = []
  voltageData.final = []
  currentData.primary = []
  currentData.intermediate = []
  currentData.final = []

  // 重置进度记录
  lastPhase.value = null
  lastProgress.value = 0

  clearTimers()
  updatePhaseData()
}

const changePhase = (phase) => {
  currentPhase.value = phase
  progress.value = 0
  // 不重置lastPhase，让数据累积逻辑自动处理阶段切换
  updatePhaseData()
  if (isPlaying.value) {
    startPhaseTimer()
  }
}

// 累积数据更新 - 保持历史数据
const updatePhaseData = () => {
  const phase = currentPhase.value
  const currentProgress = progress.value / 100 // 转换为0-1之间的值

  // 如果阶段改变，重置进度记录但保留历史数据
  if (lastPhase.value !== phase) {
    lastPhase.value = phase
    lastProgress.value = 0
  }

  // 只有当进度前进时才添加新数据点
  if (currentProgress > lastProgress.value) {
    // 生成当前阶段的新数据
    const newVoltageData = {
      primary: generateVoltageData('PRIMARY', phase, currentProgress),
      intermediate: generateVoltageData('INTERMEDIATE', phase, currentProgress),
      final: generateVoltageData('FINAL', phase, currentProgress)
    }

    const newCurrentData = {
      primary: generateCurrentData('PRIMARY', phase, currentProgress),
      intermediate: generateCurrentData('INTERMEDIATE', phase, currentProgress),
      final: generateCurrentData('FINAL', phase, currentProgress)
    }

    // 累积数据：将新数据追加到现有数据
    appendDataPoints(voltageData, newVoltageData, lastProgress.value, currentProgress)
    appendDataPoints(currentData, newCurrentData, lastProgress.value, currentProgress)

    lastProgress.value = currentProgress
  }

  // 更新其他数据
  pulseData.value = generatePulseData(phase, currentProgress)

  if (phase === EXPERIMENT_PHASES.FIELD_ESTABLISHMENT) {
    fieldStrengthData.value = generateFieldStrengthData(currentProgress)
    marxData.value = generateMarxWaveform(currentProgress)
    reportSummary.value = generateReportSummary()
  }
}

// 辅助函数：将新数据点追加到现有数据
const appendDataPoints = (existingData, newData, lastProg, currentProg) => {
  const types = ['primary', 'intermediate', 'final']

  types.forEach(type => {
    const newPoints = newData[type]
    const existingPoints = existingData[type]

    if (newPoints && newPoints.length > 0) {
      // 计算时间偏移，确保新数据接续在现有数据之后
      const timeOffset = existingPoints.length > 0 ?
        existingPoints[existingPoints.length - 1].time : 0

      // 过滤出新的数据点（基于进度变化）
      const progressDiff = currentProg - lastProg
      const startIndex = Math.floor(newPoints.length * (1 - progressDiff))

      const pointsToAdd = newPoints.slice(startIndex).map(point => ({
        ...point,
        time: point.time + timeOffset
      }))

      existingData[type].push(...pointsToAdd)
    }
  })
}

// 启动阶段计时器
const startPhaseTimer = () => {
  clearTimers()

  const phaseConfig = PHASE_CONFIG[currentPhase.value]
  const phaseDuration = phaseConfig ? phaseConfig.duration : null
  if (!phaseDuration) {
    // 如果没有持续时间，直接更新数据并停止
    updatePhaseData()
    return
  }

  const startTime = Date.now()

  const updateProgress = () => {
    const elapsed = Date.now() - startTime
    progress.value = Math.min((elapsed / phaseDuration) * 100, 100)

    // 根据当前进度更新数据
    updatePhaseData()

    if (progress.value >= 100) {
      // 自动进入下一阶段
      const phases = Object.values(EXPERIMENT_PHASES)
      const currentIndex = phases.indexOf(currentPhase.value)
      if (currentIndex < phases.length - 1) {
        currentPhase.value = phases[currentIndex + 1]
        progress.value = 0
        updatePhaseData()
        startPhaseTimer()
      } else {
        // 电场建立阶段完成后，继续更新数据但不进入下一阶段
        if (currentPhase.value === EXPERIMENT_PHASES.FIELD_ESTABLISHMENT) {
          startFieldEstablishmentTimer()
        } else {
          isPlaying.value = false
        }
      }
    } else {
      phaseTimer = requestAnimationFrame(updateProgress)
    }
  }

  phaseTimer = requestAnimationFrame(updateProgress)
}

// 电场建立阶段的持续更新计时器
const startFieldEstablishmentTimer = () => {
  const updateFieldData = () => {
    if (currentPhase.value === EXPERIMENT_PHASES.FIELD_ESTABLISHMENT && isPlaying.value) {
      updatePhaseData()
      phaseTimer = requestAnimationFrame(updateFieldData)
    }
  }
  phaseTimer = requestAnimationFrame(updateFieldData)
}

// 清除定时器
const clearTimers = () => {
  if (phaseTimer) {
    cancelAnimationFrame(phaseTimer)
    phaseTimer = null
  }
  if (dataUpdateTimer) {
    clearInterval(dataUpdateTimer)
    dataUpdateTimer = null
  }
}

// 生命周期
onMounted(() => {
  updatePhaseData()
})

onUnmounted(() => {
  clearTimers()
})
</script>

<style scoped>
.experiment-dashboard {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 2px;
  gap: 2px;
  height: calc(100vh - 54px);
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;
  position: relative;
  left: 0;
  top: 0;
}

.dashboard-layout {
  display: grid;
  grid-template-columns: minmax(0, 1fr) minmax(0, 2fr) minmax(0, 1fr);
  gap: 2px;
  flex: 1;
  min-height: 0;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  position: relative;
  left: 0;
  top: 0;
}

.left-column,
.right-column {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-height: 0;
  height: 100%;
  width: 100%;
}

.left-column > .section-item,
.right-column > .section-item {
  flex: 1;
  min-height: 0;
  height: calc((100% - 16px) / 3);
}

.center-column {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-height: 0;
  height: 100%;
  width: 100%;
}

.center-top {
  flex: 2;
  min-height: 0;
  height: calc((100% - 8px) * 2 / 3);
}

.center-bottom {
  flex: 1;
  display: flex;
  gap: 4px;
  min-height: 0;
  height: calc((100% - 4px) / 3);
}

.center-bottom-left,
.center-bottom-right {
  flex: 1;
  min-height: 0;
  width: calc((100% - 4px) / 2);
}

.center-bottom-full {
  flex: 1;
  min-height: 0;
  width: 100%;
}

.section-item {
  background: linear-gradient(135deg, rgba(10, 25, 46, 0.9) 0%, rgba(15, 35, 60, 0.8) 100%);
  border: 2px solid transparent;
  border-radius: 6px;
  padding: 8px;
  box-shadow:
    0 0 20px rgba(0, 240, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 0;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}

.section-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 12px;
  padding: 2px;
  background: linear-gradient(45deg, #00F0FF, #39FF14, #18FFFF, #7C4DFF);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  z-index: -1;
}

.section-item:hover {
  transform: translateY(-2px);
  box-shadow:
    0 8px 32px rgba(0, 240, 255, 0.4),
    0 0 40px rgba(57, 255, 20, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.section-item:hover {
  border-color: #39FF14;
  box-shadow: 0 0 30px rgba(57, 255, 20, 0.3);
  transform: translateY(-2px);
}

.device-status-container {
  height: 100% !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .dashboard-layout {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
    gap: 6px;
  }
  
  .left-column,
  .right-column,
  .center-column {
    height: auto;
    min-height: 300px;
  }
  
  .center-bottom {
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .experiment-dashboard {
    padding: 4px;
    gap: 4px;
  }
  
  .dashboard-layout {
    gap: 4px;
  }
  
  .section-item {
    padding: 8px;
    border-radius: 6px;
  }
  
  .left-column > .section-item,
  .right-column > .section-item {
    height: auto;
    min-height: 150px;
  }
}
</style>
