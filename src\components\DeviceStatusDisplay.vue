<template>
  <div class="device-status-display">
    <div class="status-header">
      <h3 class="status-title">装置实时状态</h3>
      <div class="status-indicator" :class="statusClass">
        <div class="indicator-dot"></div>
        <span class="status-text">{{ statusText }}</span>
      </div>
    </div>

    <div class="status-content">
      <!-- 当前阶段信息 -->
      <div class="phase-info">
        <div class="phase-name">{{ phaseDisplayName }}</div>
        <div class="phase-progress">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: progress + '%' }"></div>
          </div>
          <span class="progress-text">{{ Math.round(progress) }}%</span>
        </div>
      </div>

      <!-- 系统参数 -->
      <div class="system-params">
        <!-- 当前阶段状态 -->
        <div class="param-group current-stage">
          <h4 class="group-title">{{ getCurrentStageTitle() }}</h4>
          <div class="stage-description">{{ getCurrentStageDescription() }}</div>
          <div class="param-list">
            <div class="param-item" v-for="status in getCurrentStageStatus()" :key="status.label">
              <span class="param-label">{{ status.label }}</span>
              <div class="param-status">
                <span class="status-dot" :class="status.statusClass"></span>
                <span class="param-value">{{ status.value }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 电容组状态 -->
        <div class="param-group">
          <h4 class="group-title">电容组状态</h4>
          <div class="param-list">
            <div class="param-item">
              <span class="param-label">一级储能组</span>
              <div class="param-status">
                <span class="status-dot" :class="getCapacitorStatus('primary')"></span>
                <span class="param-value">{{ formatVoltage(getCurrentVoltage('primary')) }}</span>
              </div>
            </div>
            <div class="param-item">
              <span class="param-label">中储电容组</span>
              <div class="param-status">
                <span class="status-dot" :class="getCapacitorStatus('intermediate')"></span>
                <span class="param-value">{{ formatVoltage(getCurrentVoltage('intermediate')) }}</span>
              </div>
            </div>
            <div class="param-item">
              <span class="param-label">风化电容组</span>
              <div class="param-status">
                <span class="status-dot" :class="getCapacitorStatus('final')"></span>
                <span class="param-value">{{ formatVoltage(getCurrentVoltage('final')) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 高压开关系统 -->
        <div class="param-group">
          <h4 class="group-title">高压开关系统</h4>
          <div class="param-list">
            <div class="param-item">
              <span class="param-label">一级开关组</span>
              <div class="param-status">
                <span class="status-dot" :class="getPrimarySwitchStatus()"></span>
                <span class="param-value">{{ getPrimarySwitchText() }}</span>
              </div>
            </div>
            <div class="param-item">
              <span class="param-label">中储开关组</span>
              <div class="param-status">
                <span class="status-dot" :class="getIntermediateSwitchStatus()"></span>
                <span class="param-value">{{ getIntermediateSwitchText() }}</span>
              </div>
            </div>
            <div class="param-item">
              <span class="param-label">击穿电压</span>
              <div class="param-status">
                <span class="status-dot" :class="getBreakdownStatus()"></span>
                <span class="param-value">{{ getBreakdownVoltage() }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 传输管道系统 -->
        <div class="param-group">
          <h4 class="group-title">传输管道系统</h4>
          <div class="param-list">
            <div class="param-item">
              <span class="param-label">管道压力</span>
              <div class="param-status">
                <span class="status-dot" :class="getPipelinePressureStatus()"></span>
                <span class="param-value">{{ getPipelinePressure() }} MPa</span>
              </div>
            </div>
            <div class="param-item">
              <span class="param-label">绝缘层状态</span>
              <div class="param-status">
                <span class="status-dot" :class="getInsulationStatus()"></span>
                <span class="param-value">{{ getInsulationText() }}</span>
              </div>
            </div>
            <div class="param-item">
              <span class="param-label">电流传递</span>
              <div class="param-status">
                <span class="status-dot" :class="getCurrentTransferStatus()"></span>
                <span class="param-value">{{ getCurrentTransferText() }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 电路柜系统 -->
        <div class="param-group">
          <h4 class="group-title">电路柜系统</h4>
          <div class="param-list">
            <div class="param-item">
              <span class="param-label">充电电路</span>
              <div class="param-status">
                <span class="status-dot" :class="getChargingCircuitStatus()"></span>
                <span class="param-value">{{ getChargingCircuitText() }}</span>
              </div>
            </div>
            <div class="param-item">
              <span class="param-label">触发器激活</span>
              <div class="param-status">
                <span class="status-dot" :class="getTriggerActivationStatus()"></span>
                <span class="param-value">{{ getTriggerActivationText() }}</span>
              </div>
            </div>
            <div class="param-item">
              <span class="param-label">辅助电流</span>
              <div class="param-status">
                <span class="status-dot" :class="getAuxiliaryCurrentStatus()"></span>
                <span class="param-value">{{ getAuxiliaryCurrentText() }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 外仓绝缘系统 -->
        <div class="param-group">
          <h4 class="group-title">外仓绝缘系统</h4>
          <div class="param-list">
            <div class="param-item">
              <span class="param-label">外仓状态</span>
              <div class="param-status">
                <span class="status-dot" :class="getOuterChamberStatus()"></span>
                <span class="param-value">{{ getOuterChamberText() }}</span>
              </div>
            </div>
            <div class="param-item">
              <span class="param-label">接触面电流</span>
              <div class="param-status">
                <span class="status-dot" :class="getContactCurrentStatus()"></span>
                <span class="param-value">{{ getContactCurrentText() }}</span>
              </div>
            </div>
            <div class="param-item">
              <span class="param-label">绝缘监测</span>
              <div class="param-status">
                <span class="status-dot status-normal"></span>
                <span class="param-value">正常</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { PHASE_CONFIG, EXPERIMENT_PHASES } from '../config/experimentConfig.js'

const props = defineProps({
  currentPhase: {
    type: String,
    required: true
  },
  progress: {
    type: Number,
    default: 0
  },
  voltageData: {
    type: Object,
    required: true
  },
  currentData: {
    type: Object,
    required: true
  },
  isPlaying: {
    type: Boolean,
    default: false
  }
})

// 计算属性
const phaseDisplayName = computed(() => {
  return PHASE_CONFIG[props.currentPhase]?.name || '未知阶段'
})

const statusClass = computed(() => {
  if (!props.isPlaying) return 'status-stopped'
  if (props.progress >= 100) return 'status-complete'
  return 'status-running'
})

const statusText = computed(() => {
  if (!props.isPlaying) return '待机'
  if (props.progress >= 100) return '完成'
  return '运行中'
})

// 方法
const getCurrentVoltage = (type) => {
  const data = props.voltageData[type]
  return data && data.length > 0 ? data[data.length - 1].value : 0
}

const getCurrentCurrent = (type) => {
  const data = props.currentData[type]
  return data && data.length > 0 ? data[data.length - 1].value : 0
}

const formatVoltage = (value) => {
  if (value >= 1000000) {
    return (value / 1000000).toFixed(1) + 'MV'
  } else if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'kV'
  }
  return value.toFixed(0) + 'V'
}

const formatCurrent = (value) => {
  if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'kA'
  }
  return value.toFixed(0) + 'A'
}

const getSystemTemperature = () => {
  // 模拟系统温度，根据运行状态和进度变化
  const baseTemp = 25
  const runningTemp = props.isPlaying ? 5 + (props.progress / 100) * 10 : 0
  return (baseTemp + runningTemp).toFixed(1)
}

// 电容器状态
const getCapacitorStatus = (type) => {
  const voltage = getCurrentVoltage(type)
  const maxVoltages = { primary: 1000000, intermediate: 30000000, final: 100000000 }
  const ratio = voltage / maxVoltages[type]

  if (ratio < 0.1) return 'status-stopped'
  if (ratio < 0.8) return 'status-charging'
  return 'status-ready'
}

// 压缩空气系统状态
const getAirSystemStatus = () => {
  const pressure = getAirPressure()
  if (pressure < 0.5) return 'status-warning'
  if (pressure < 0.7) return 'status-charging'
  return 'status-normal'
}

const getAirPressure = () => {
  // 根据实验阶段模拟气压变化
  const basePressure = 0.8
  if (!props.isPlaying) return basePressure.toFixed(2)

  const phaseMultiplier = {
    'preparation': 1.0,
    'primary_charging': 0.95,
    'intermediate_charging': 0.9,
    'final_charging': 0.85,
    'field_establishment': 0.8
  }

  const multiplier = phaseMultiplier[props.currentPhase] || 1.0
  return (basePressure * multiplier).toFixed(2)
}

// 开关状态
const getSwitchStatus = () => {
  if (props.currentPhase === 'field_establishment') return 'status-running'
  if (props.isPlaying) return 'status-ready'
  return 'status-stopped'
}

const getSwitchStatusText = () => {
  if (props.currentPhase === 'field_establishment') return '导通'
  if (props.isPlaying) return '就绪'
  return '断开'
}

// 油处理系统
const getOilTempStatus = () => {
  const temp = getOilTemperature()
  if (temp > 60) return 'status-warning'
  if (temp > 45) return 'status-charging'
  return 'status-normal'
}

const getOilTemperature = () => {
  const baseTemp = 35
  const runningTemp = props.isPlaying ? (props.progress / 100) * 15 : 0
  return (baseTemp + runningTemp).toFixed(1)
}

// 触发系统
const getTriggerStatus = () => {
  if (props.currentPhase === 'field_establishment') return 'status-running'
  if (props.currentPhase === 'final_charging' && props.progress > 80) return 'status-ready'
  if (props.isPlaying) return 'status-charging'
  return 'status-stopped'
}

const getTriggerStatusText = () => {
  if (props.currentPhase === 'field_establishment') return '已触发'
  if (props.currentPhase === 'final_charging' && props.progress > 80) return '就绪'
  if (props.isPlaying) return '准备中'
  return '待机'
}

const getTriggerDelay = () => {
  // 根据阶段返回不同的触发延时
  const delays = {
    'preparation': 0,
    'primary_charging': 10,
    'intermediate_charging': 5,
    'final_charging': 2,
    'field_establishment': 0.5
  }
  return delays[props.currentPhase] || 0
}

// 当前阶段标题和描述
const getCurrentStageTitle = () => {
  const titles = {
    'preparation': '准备阶段',
    'primary_charging': '一级压缩阶段',
    'intermediate_charging': '二级压缩阶段',
    'final_charging': '三级压缩阶段',
    'field_establishment': '电场建立阶段'
  }
  return titles[props.currentPhase] || '未知阶段'
}

const getCurrentStageDescription = () => {
  const descriptions = {
    'preparation': '系统初始化，设备状态检查',
    'primary_charging': '电路柜向电容组充电，并联形式充满电流',
    'intermediate_charging': '电流流向中储结构，六个电容逐渐充满电压',
    'final_charging': '电流通过传输管道进入风化电容结构',
    'field_establishment': '风化电容激活，电流通过外仓传递'
  }
  return descriptions[props.currentPhase] || ''
}

const getCurrentStageStatus = () => {
  const phase = props.currentPhase
  const progress = props.progress

  switch (phase) {
    case 'preparation':
      return [
        { label: '系统检查', statusClass: 'status-normal', value: '完成' },
        { label: '设备预热', statusClass: progress > 50 ? 'status-normal' : 'status-charging', value: progress > 50 ? '完成' : '进行中' }
      ]

    case 'primary_charging':
      return [
        { label: '电路柜充电', statusClass: 'status-running', value: '进行中' },
        { label: '并联充电', statusClass: progress > 30 ? 'status-running' : 'status-stopped', value: progress > 30 ? '进行中' : '待机' },
        { label: '电压监测', statusClass: progress > 60 ? 'status-ready' : 'status-charging', value: `${Math.round(progress)}%` },
        { label: '触发准备', statusClass: progress > 80 ? 'status-ready' : 'status-stopped', value: progress > 80 ? '就绪' : '待机' }
      ]

    case 'intermediate_charging':
      return [
        { label: '中储充电', statusClass: 'status-running', value: '进行中' },
        { label: '六电容组', statusClass: progress > 40 ? 'status-running' : 'status-charging', value: `${Math.min(6, Math.floor(progress / 16.7))}个充满` },
        { label: '开关击穿', statusClass: progress > 70 ? 'status-running' : 'status-stopped', value: progress > 70 ? '激活' : '待机' },
        { label: '电压下降', statusClass: progress > 90 ? 'status-running' : 'status-stopped', value: progress > 90 ? '进行中' : '待机' }
      ]

    case 'final_charging':
      return [
        { label: '管道传输', statusClass: 'status-running', value: '进行中' },
        { label: '风化充电', statusClass: progress > 30 ? 'status-running' : 'status-charging', value: progress > 30 ? '充电中' : '准备中' },
        { label: '电容激活', statusClass: progress > 70 ? 'status-ready' : 'status-charging', value: progress > 70 ? '激活' : '充电中' },
        { label: '外仓接触', statusClass: progress > 90 ? 'status-running' : 'status-stopped', value: progress > 90 ? '接触' : '待机' }
      ]

    case 'field_establishment':
      return [
        { label: '电场强度', statusClass: 'status-running', value: '建立中' },
        { label: '电流传递', statusClass: 'status-running', value: '活跃' },
        { label: '数据采集', statusClass: 'status-normal', value: '正常' }
      ]

    default:
      return []
  }
}

// 高压开关系统状态
const getPrimarySwitchStatus = () => {
  if (props.currentPhase === 'primary_charging' && props.progress > 80) return 'status-running'
  if (props.currentPhase === 'intermediate_charging') return 'status-ready'
  return 'status-stopped'
}

const getPrimarySwitchText = () => {
  if (props.currentPhase === 'primary_charging' && props.progress > 80) return '击穿中'
  if (props.currentPhase === 'intermediate_charging') return '已击穿'
  return '待机'
}

const getIntermediateSwitchStatus = () => {
  if (props.currentPhase === 'intermediate_charging' && props.progress > 70) return 'status-running'
  if (props.currentPhase === 'final_charging') return 'status-ready'
  return 'status-stopped'
}

const getIntermediateSwitchText = () => {
  if (props.currentPhase === 'intermediate_charging' && props.progress > 70) return '击穿中'
  if (props.currentPhase === 'final_charging') return '已击穿'
  return '待机'
}

const getBreakdownStatus = () => {
  if (props.currentPhase === 'primary_charging' && props.progress > 60) return 'status-ready'
  if (props.currentPhase === 'intermediate_charging' && props.progress > 40) return 'status-ready'
  if (props.currentPhase === 'final_charging' && props.progress > 30) return 'status-ready'
  return 'status-stopped'
}

const getBreakdownVoltage = () => {
  const voltages = {
    'preparation': '0kV',
    'primary_charging': `${Math.round(props.progress * 8)}kV`,
    'intermediate_charging': `${Math.round(props.progress * 15)}kV`,
    'final_charging': `${Math.round(props.progress * 25)}kV`,
    'field_establishment': '2.5MV'
  }
  return voltages[props.currentPhase] || '0kV'
}

// 传输管道系统状态
const getPipelinePressureStatus = () => {
  if (props.currentPhase === 'final_charging') return 'status-running'
  if (props.currentPhase === 'field_establishment') return 'status-ready'
  return 'status-normal'
}

const getPipelinePressure = () => {
  const basePressure = 0.6
  if (props.currentPhase === 'final_charging') {
    return (basePressure + (props.progress / 100) * 0.4).toFixed(2)
  }
  return basePressure.toFixed(2)
}

const getInsulationStatus = () => {
  if (props.currentPhase === 'final_charging' && props.progress > 90) return 'status-running'
  if (props.currentPhase === 'field_establishment') return 'status-running'
  return 'status-normal'
}

const getInsulationText = () => {
  if (props.currentPhase === 'final_charging' && props.progress > 90) return '接触中'
  if (props.currentPhase === 'field_establishment') return '传导中'
  return '正常'
}

const getCurrentTransferStatus = () => {
  if (props.currentPhase === 'final_charging' && props.progress > 70) return 'status-running'
  if (props.currentPhase === 'field_establishment') return 'status-running'
  return 'status-stopped'
}

const getCurrentTransferText = () => {
  if (props.currentPhase === 'final_charging' && props.progress > 70) return '传递中'
  if (props.currentPhase === 'field_establishment') return '活跃'
  return '待机'
}

// 电路柜系统状态
const getChargingCircuitStatus = () => {
  if (props.currentPhase === 'primary_charging') return 'status-running'
  if (props.currentPhase === 'intermediate_charging' || props.currentPhase === 'final_charging') return 'status-ready'
  return 'status-stopped'
}

const getChargingCircuitText = () => {
  if (props.currentPhase === 'primary_charging') return '充电中'
  if (props.currentPhase === 'intermediate_charging' || props.currentPhase === 'final_charging') return '就绪'
  return '待机'
}

const getTriggerActivationStatus = () => {
  if (props.currentPhase === 'primary_charging' && props.progress > 60) return 'status-ready'
  if (props.currentPhase === 'intermediate_charging' && props.progress > 40) return 'status-ready'
  if (props.currentPhase === 'final_charging' && props.progress > 30) return 'status-ready'
  return 'status-stopped'
}

const getTriggerActivationText = () => {
  if (props.currentPhase === 'primary_charging' && props.progress > 80) return '已激活'
  if (props.currentPhase === 'intermediate_charging' && props.progress > 70) return '已激活'
  if (props.currentPhase === 'final_charging' && props.progress > 70) return '已激活'
  if (props.currentPhase === 'primary_charging' && props.progress > 60) return '准备中'
  if (props.currentPhase === 'intermediate_charging' && props.progress > 40) return '准备中'
  if (props.currentPhase === 'final_charging' && props.progress > 30) return '准备中'
  return '待机'
}

const getAuxiliaryCurrentStatus = () => {
  if (props.currentPhase === 'primary_charging' && props.progress > 80) return 'status-running'
  if (props.currentPhase === 'intermediate_charging' && props.progress > 70) return 'status-running'
  return 'status-stopped'
}

const getAuxiliaryCurrentText = () => {
  if (props.currentPhase === 'primary_charging' && props.progress > 80) return '辅助中'
  if (props.currentPhase === 'intermediate_charging' && props.progress > 70) return '辅助中'
  return '待机'
}

// 外仓绝缘系统状态
const getOuterChamberStatus = () => {
  if (props.currentPhase === 'final_charging' && props.progress > 90) return 'status-running'
  if (props.currentPhase === 'field_establishment') return 'status-running'
  return 'status-normal'
}

const getOuterChamberText = () => {
  if (props.currentPhase === 'final_charging' && props.progress > 90) return '接触准备'
  if (props.currentPhase === 'field_establishment') return '电流传递'
  return '正常'
}

const getContactCurrentStatus = () => {
  if (props.currentPhase === 'field_establishment') return 'status-running'
  if (props.currentPhase === 'final_charging' && props.progress > 90) return 'status-ready'
  return 'status-stopped'
}

const getContactCurrentText = () => {
  if (props.currentPhase === 'field_establishment') return '传递中'
  if (props.currentPhase === 'final_charging' && props.progress > 90) return '准备中'
  return '待机'
}
</script>

<style scoped>
.device-status-display {
  height: 100%;
  display: flex;
  flex-direction: column;
  color: #00F0FF;
  font-family: 'Arial', sans-serif;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 240, 255, 0.3);
}

.status-title {
  font-size: 20px;
  font-weight: bold;
  margin: 0;
  color: #00F0FF;
  text-shadow: 0 0 10px rgba(0, 240, 255, 0.5);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.indicator-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-running .indicator-dot {
  background: #39FF14;
  box-shadow: 0 0 10px rgba(57, 255, 20, 0.5);
}

.status-stopped .indicator-dot {
  background: #666;
  animation: none;
}

.status-complete .indicator-dot {
  background: #00F0FF;
  box-shadow: 0 0 10px rgba(0, 240, 255, 0.5);
}

.status-text {
  font-size: 16px;
  font-weight: 500;
}

.status-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.phase-info {
  background: rgba(0, 240, 255, 0.1);
  border: 1px solid rgba(0, 240, 255, 0.3);
  border-radius: 6px;
  padding: 14px;
}

.phase-name {
  font-size: 18px;
  font-weight: bold;
  color: #39FF14;
  margin-bottom: 10px;
  text-align: center;
  text-shadow: 0 0 8px rgba(57, 255, 20, 0.4);
}

.phase-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: rgba(0, 240, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #00F0FF, #39FF14);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 14px;
  color: #00F0FF;
  min-width: 40px;
  text-align: right;
  font-weight: bold;
}

.system-params {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  overflow-y: auto;
}

.param-group {
  background: rgba(57, 255, 20, 0.05);
  border: 1px solid rgba(57, 255, 20, 0.2);
  border-radius: 6px;
  padding: 12px;
  min-height: 0;
}

.param-group:nth-child(1) {
  grid-column: 1 / -1;
}

.param-group.current-stage {
  background: rgba(0, 240, 255, 0.08);
  border: 1px solid rgba(0, 240, 255, 0.3);
  box-shadow: 0 0 15px rgba(0, 240, 255, 0.2);
}

.stage-description {
  font-size: 12px;
  color: #18FFFF;
  text-align: center;
  margin-bottom: 10px;
  padding: 6px;
  background: rgba(0, 240, 255, 0.1);
  border-radius: 4px;
  font-style: italic;
}

.group-title {
  font-size: 15px;
  font-weight: bold;
  color: #39FF14;
  margin: 0 0 10px 0;
  text-align: center;
  text-shadow: 0 0 8px rgba(57, 255, 20, 0.4);
}

.param-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.param-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  padding: 4px 0;
}

.param-label {
  color: #00F0FF;
  font-weight: 500;
  flex: 1;
}

.param-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-dot.status-normal {
  background: #4CAF50;
  box-shadow: 0 0 6px rgba(76, 175, 80, 0.5);
}

.status-dot.status-running {
  background: #39FF14;
  box-shadow: 0 0 6px rgba(57, 255, 20, 0.5);
  animation: pulse 1.5s infinite;
}

.status-dot.status-ready {
  background: #00F0FF;
  box-shadow: 0 0 6px rgba(0, 240, 255, 0.5);
}

.status-dot.status-charging {
  background: #FFC400;
  box-shadow: 0 0 6px rgba(255, 196, 0, 0.5);
  animation: pulse 2s infinite;
}

.status-dot.status-warning {
  background: #FF9800;
  box-shadow: 0 0 6px rgba(255, 152, 0, 0.5);
  animation: pulse 1s infinite;
}

.status-dot.status-stopped {
  background: #666;
  box-shadow: none;
}

.param-values {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.param-value {
  color: #39FF14;
  font-weight: bold;
  font-size: 13px;
}

.param-value.voltage {
  color: #18FFFF;
}

.param-value.current {
  color: #E91E63;
}

.param-value.status-running {
  color: #39FF14;
}

.param-value.status-stopped {
  color: #666;
}

.param-value.status-safe {
  color: #4CAF50;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* 响应式调整 */
@media (max-width: 1400px) {
  .system-params {
    grid-template-columns: 1fr;
  }

  .param-group:nth-child(1) {
    grid-column: 1;
  }
}

@media (max-width: 1200px) {
  .status-title {
    font-size: 18px;
  }

  .phase-name {
    font-size: 16px;
  }

  .group-title {
    font-size: 13px;
  }

  .param-item {
    font-size: 12px;
  }

  .param-value {
    font-size: 11px;
  }

  .status-dot {
    width: 8px;
    height: 8px;
  }

  .param-group {
    padding: 10px;
  }

  .system-params {
    gap: 8px;
  }
}
</style>
