<template>
  <div class="control-panel">
    <!-- 顶部流程进度条 -->
    <div class="top-progress">
      <div class="flow-line">
        <div class="flow-progress" :style="{ width: overallProgress + '%' }"></div>
      </div>
    </div>

    <!-- 主控制区域 -->
    <div class="main-control-area">
      <!-- 左侧：阶段指示器 -->
      <div class="phase-section">
        <div class="phase-indicators">
          <div
            v-for="(phase, key) in PHASE_CONFIG"
            :key="key"
            class="phase-indicator"
            :class="{
              active: currentPhase === key,
              completed: isPhaseCompleted(key)
            }"
            @click="$emit('phase-change', key)"
          >
            <div class="phase-dot"></div>
            <span class="phase-name">{{ phase.name }}</span>
          </div>
        </div>
      </div>

      <!-- 中间：控制按钮 -->
      <div class="control-section">
        <div class="main-controls">
          <button
            @click="handlePlayClick"
            v-if="!isPlaying"
            class="control-btn play-btn"
          >
            <span class="btn-icon">▶</span>
            <span class="btn-text">开始</span>
          </button>

          <button
            @click="handlePauseClick"
            v-if="isPlaying"
            class="control-btn pause-btn"
          >
            <span class="btn-icon">⏸</span>
            <span class="btn-text">暂停</span>
          </button>

          <button
            @click="handleResetClick"
            class="control-btn reset-btn"
          >
            <span class="btn-icon">⏹</span>
            <span class="btn-text">重置</span>
          </button>
        </div>

        <!-- 当前进度 -->
        <div class="current-progress">
          <span class="progress-text">{{ progress.toFixed(1) }}%</span>
        </div>
      </div>

      <!-- 右侧：状态信息 -->
      <div class="status-section">
        <div class="phase-info">
          <div class="phase-title">{{ currentPhaseConfig?.name }}</div>
          <div class="phase-description">{{ currentPhaseConfig?.description }}</div>
        </div>

        <div class="system-status">
          <div class="status-item">
            <span class="status-label">状态:</span>
            <span class="status-value" :class="systemStatusClass">{{ systemStatusText }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">时间:</span>
            <span class="status-value">{{ formatRuntime(runtime) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { EXPERIMENT_PHASES, PHASE_CONFIG } from '../config/experimentConfig.js'

// Props
const props = defineProps({
  currentPhase: {
    type: String,
    required: true
  },
  isPlaying: {
    type: Boolean,
    default: false
  },
  progress: {
    type: Number,
    default: 0
  }
})

// Emits
const emit = defineEmits(['play', 'pause', 'reset', 'phase-change'])

// 响应式数据
const runtime = ref(0)
let runtimeTimer = null

// 计算属性
const currentPhaseConfig = computed(() => {
  return PHASE_CONFIG[props.currentPhase]
})

const systemStatusText = computed(() => {
  if (props.isPlaying) return '运行中'
  if (props.progress > 0) return '已暂停'
  return '待机'
})

const systemStatusClass = computed(() => {
  if (props.isPlaying) return 'status-running'
  if (props.progress > 0) return 'status-paused'
  return 'status-idle'
})

const overallProgress = computed(() => {
  const phases = Object.keys(PHASE_CONFIG)
  const currentIndex = phases.indexOf(props.currentPhase)
  const totalPhases = phases.length

  if (currentIndex === -1) return 0

  const completedPhases = currentIndex
  const currentPhaseProgress = props.progress / 100

  return ((completedPhases + currentPhaseProgress) / totalPhases) * 100
})

// 方法
const handlePlayClick = () => {
  console.log('Play button clicked in ControlPanel')
  emit('play')
}

const handlePauseClick = () => {
  console.log('Pause button clicked in ControlPanel')
  emit('pause')
}

const handleResetClick = () => {
  console.log('Reset button clicked in ControlPanel')
  emit('reset')
}

const isPhaseCompleted = (phaseKey) => {
  const phases = Object.keys(PHASE_CONFIG)
  const currentIndex = phases.indexOf(props.currentPhase)
  const phaseIndex = phases.indexOf(phaseKey)
  return phaseIndex < currentIndex || (phaseIndex === currentIndex && props.progress >= 100)
}

const formatDuration = (ms) => {
  const seconds = ms / 1000
  return `${seconds}秒`
}

const formatRuntime = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 运行时间计时器
const startRuntimeTimer = () => {
  runtimeTimer = setInterval(() => {
    if (props.isPlaying) {
      runtime.value++
    }
  }, 1000)
}

const stopRuntimeTimer = () => {
  if (runtimeTimer) {
    clearInterval(runtimeTimer)
    runtimeTimer = null
  }
}

// 生命周期
onMounted(() => {
  startRuntimeTimer()
})

onUnmounted(() => {
  stopRuntimeTimer()
})

// 监听播放状态变化
const handlePlayStateChange = () => {
  if (!props.isPlaying && props.progress === 0) {
    runtime.value = 0
  }
}

// 暴露给父组件
defineExpose({
  handlePlayStateChange
})
</script>

<style scoped>
.control-panel {
  display: flex;
  flex-direction: column;
  background: rgba(10, 25, 46, 0.95);
  border: 1px solid #00F0FF;
  border-radius: 12px;
  padding: 12px 20px;
  box-shadow: 0 0 20px rgba(0, 240, 255, 0.4);
  backdrop-filter: blur(20px);
  gap: 8px;
  margin: 0;
  width: 100%;
}

/* 顶部进度条 */
.top-progress {
  width: 100%;
  margin-bottom: 4px;
}

/* 主控制区域 */
.main-control-area {
  display: grid;
  grid-template-columns: 2fr 1fr 2fr;
  gap: 20px;
  align-items: center;
}

.phase-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.control-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.status-section {
  display: flex;
  flex-direction: column;
  gap: 6px;
  text-align: right;
}

/* 流程进度条 */
.flow-line {
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.flow-progress {
  height: 100%;
  background: linear-gradient(90deg, #00F0FF, #39FF14, #18FFFF, #7C4DFF);
  border-radius: 2px;
  transition: width 0.8s ease;
  position: relative;
  box-shadow: 0 0 12px rgba(0, 240, 255, 0.8);
}

.flow-progress::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 30px;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4));
  animation: flowShine 2s ease-in-out infinite;
}

@keyframes flowShine {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}

/* 阶段指示器 */
.phase-indicators {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.phase-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 3px 6px;
  border-radius: 6px;
  font-size: 11px;
  white-space: nowrap;
}

.phase-indicator:hover {
  background: rgba(0, 240, 255, 0.1);
}

.phase-indicator.active {
  background: rgba(0, 240, 255, 0.2);
}

.phase-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #666;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.phase-indicator.active .phase-dot {
  background: #00F0FF;
  box-shadow: 0 0 8px #00F0FF;
}

.phase-indicator.completed .phase-dot {
  background: #39FF14;
  box-shadow: 0 0 8px #39FF14;
}

.phase-name {
  font-size: 9px;
  color: #ccc;
  white-space: nowrap;
}

.phase-indicator.active .phase-name {
  color: #00F0FF;
  font-weight: bold;
}

.phase-indicator.completed .phase-name {
  color: #39FF14;
}

/* 主控制按钮 */
.main-controls {
  display: flex;
  gap: 8px;
}

.control-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.play-btn {
  background: linear-gradient(45deg, #39FF14, #18FFFF);
  color: #0A192E;
}

.pause-btn {
  background: linear-gradient(45deg, #FFC400, #FF6B35);
  color: #0A192E;
}

.reset-btn {
  background: linear-gradient(45deg, #7C4DFF, #E91E63);
  color: white;
}

.control-btn:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(57, 255, 20, 0.6);
  filter: brightness(1.2);
}

.play-btn:hover {
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.4),
    0 0 25px rgba(57, 255, 20, 0.8);
}

.pause-btn:hover {
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.4),
    0 0 25px rgba(255, 196, 0, 0.8);
}

.reset-btn:hover {
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.4),
    0 0 25px rgba(124, 77, 255, 0.8);
}

.btn-icon {
  font-size: 16px;
}

/* 当前进度显示 */
.current-progress {
  margin-top: 4px;
}

.progress-text {
  font-size: 14px;
  color: #00F0FF;
  font-weight: bold;
  text-shadow: 0 0 8px rgba(0, 240, 255, 0.6);
}

/* 阶段信息 */
.phase-info {
  margin-bottom: 6px;
}

.phase-title {
  font-size: 12px;
  font-weight: bold;
  color: #00F0FF;
  margin-bottom: 2px;
}

.phase-description {
  font-size: 9px;
  color: #39FF14;
  line-height: 1.2;
}

/* 系统状态 */
.system-status {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.status-item {
  display: flex;
  justify-content: flex-end;
  gap: 6px;
  align-items: center;
}

.status-label {
  font-size: 10px;
  color: #ccc;
}

.status-value {
  font-size: 10px;
  font-weight: bold;
}

.status-running {
  color: #39FF14;
}

.status-paused {
  color: #FFC400;
}

.status-idle {
  color: #666;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-control-area {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .phase-section,
  .control-section,
  .status-section {
    text-align: center;
  }

  .phase-indicators {
    justify-content: center;
  }

  .system-status .status-item {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .control-panel {
    padding: 8px 12px;
  }

  .phase-indicators {
    gap: 4px;
  }

  .phase-indicator {
    padding: 2px 4px;
  }

  .phase-name {
    font-size: 8px;
  }
}
</style>
